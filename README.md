# Secure Backend API

A production-ready Express.js backend built with TypeScript, featuring JWT authentication, Role-Based Access Control (RBAC), and a clean layered architecture. This template provides a comprehensive foundation for building secure, scalable APIs with enterprise-grade features.

## 🏗️ Architecture

This backend follows a **Layered Architecture (N-Tier)** pattern, ensuring separation of concerns and maintainability:

### Architecture Layers

```
┌─────────────────────────────────────────────────────────┐
│                    Routes Layer                         │
│              (HTTP Request Handling)                   │
├─────────────────────────────────────────────────────────┤
│                  Controllers Layer                      │
│            (Request/Response Logic)                     │
├─────────────────────────────────────────────────────────┤
│                   Services Layer                        │
│              (Business Logic)                          │
├─────────────────────────────────────────────────────────┤
│                Data Access Layer                        │
│              (Database Operations)                      │
└─────────────────────────────────────────────────────────┘
```

#### 1. **Routes Layer** (`src/routes/`)
- **Responsibility**: API endpoint definitions and routing
- **Files**: `*.route.ts` (e.g., `auth.routes.ts`, `user.route.ts`)
- **Function**: Maps HTTP methods and URLs to controller actions

#### 2. **Controllers Layer** (`src/controllers/`)
- **Responsibility**: Request/response handling and input validation
- **Files**: `*.controller.ts` (e.g., `auth.controller.ts`, `user.controller.ts`)
- **Function**: Processes HTTP requests, validates data using DTOs, calls services

#### 3. **Services Layer** (`src/services/`)
- **Responsibility**: Core business logic and orchestration
- **Files**: `*.service.ts` (e.g., `auth.service.ts`, `user.service.ts`)
- **Function**: Implements business rules, coordinates data operations

#### 4. **Data Access Layer** (`src/prisma/`, `src/repositories/`)
- **Responsibility**: Database operations and data persistence
- **Tools**: Prisma ORM for type-safe database access
- **Function**: Handles CRUD operations and complex queries

### Supporting Components

- **DTOs** (`src/dtos/`): Data validation and transformation objects
- **Middlewares** (`src/middlewares/`): Authentication, authorization, logging, error handling
- **Interfaces** (`src/interfaces/`): TypeScript type definitions
- **Utils** (`src/utils/`): Helper functions and utilities

## 🚀 Features

- **🔐 JWT Authentication** - Secure token-based authentication
- **👥 Role-Based Access Control (RBAC)** - Granular permission system
- **🛡️ Permission-Based Authorization** - Fine-grained access control
- **✉️ Email Verification** - Secure account verification flow
- **✅ Request Validation** - DTO validation with class-validator
- **🚧 Rate Limiting** - Protection against abuse and DDoS
- **📝 Comprehensive Logging** - Winston + Morgan integration
- **⚠️ Error Handling** - Centralized error management
- **🔷 TypeScript** - Full type safety throughout
- **🗄️ Prisma ORM** - Type-safe database operations
- **🔒 Security Middlewares** - Helmet, HPP, CORS protection
- **📊 API Documentation** - Swagger/OpenAPI integration
- **🧪 Testing Suite** - Jest with comprehensive test coverage
- **🔄 PM2 Support** - Production process management

## 📋 Prerequisites

- **Node.js** 18.0 or higher
- **PostgreSQL** 13.0 or higher
- **npm** or **yarn** package manager
- **Git** for version control

## 🛠️ Quick Start

### 1. Clone and Install
```bash
# Clone the repository
git clone <repository-url>
cd secure_backend

# Install dependencies
npm install
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env.development.local

# Edit your environment file
nano .env.development.local
```

**Required environment variables:**
```env
NODE_ENV=development
PORT=3000
ORIGIN=http://localhost:3000
CREDENTIALS=true

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-min-32-chars"
JWT_EXPIRES_IN=7d

# Security
BCRYPT_SALT_ROUNDS=12

# Logging
LOG_FORMAT=dev
LOG_DIR=../logs

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
```

### 3. Database Setup
```bash
# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate:dev

# (Optional) Seed the database
npm run db:seed
```

### 4. Start Development
```bash
# Start development server with hot reload
npm run dev

# Server will start at http://localhost:3000
# API documentation available at http://localhost:3000/api-docs
# Health check at http://localhost:3000/health
```

## 📋 Available Commands

### Development Commands

```bash
# Development server with hot reload
npm run dev

# Development with default env (uses .env file)
npm run dev:default

# Start built application in development mode
npm run start:dev
```

### Build Commands

```bash
# Build for production (SWC - fast)
npm run build

# Build for production (TypeScript - slower but more thorough)
npm run build:tsc
```

### Database Commands

```bash
# Generate Prisma client
npm run db:generate

# Create and run database migration (development)
npm run db:migrate:dev

# Deploy migrations (production)
npm run db:migrate:prod

# Reset database and run all migrations
npm run db:reset:dev

# Open Prisma Studio (database GUI)
npm run db:studio

# Seed the database with initial data
npm run db:seed
```

### Testing Commands

```bash
# Run all tests
npm run test

# Run tests with coverage report
npm run testAll

# Run tests in development mode (with dev env)
npm run test:dev
```

### Code Quality Commands

```bash
# Lint TypeScript files
npm run lint

# Lint and fix issues automatically
npm run lint:fix

# Format code with Prettier (handled by lint-staged)
npm run prepare
```

### Production Commands

#### Standard Node.js Deployment
```bash
# Build and start for production
npm run start

# Start built application with production env
npm run start:prod
```

#### PM2 Deployment (Recommended)

**Initial deployment:**
```bash
# Deploy to production (builds + starts with clustering)
npm run deploy:prod

# Deploy to development environment
npm run deploy:dev
```

**Application management:**
```bash
# Graceful reload (zero-downtime updates)
npm run pm2:reload:prod
npm run pm2:reload:dev

# Restart applications (with brief downtime)
npm run pm2:restart:prod
npm run pm2:restart:dev

# Stop applications
npm run pm2:stop:prod
npm run pm2:stop:dev
```

**Monitoring and logs:**
```bash
# View real-time logs
npm run pm2:logs:prod
npm run pm2:logs:dev

# Interactive monitoring dashboard
npm run pm2:monitor

# Check application status
npm run pm2:status

# Clear log files
npm run pm2:flush
```

### Environment-Specific Commands

Most commands support environment-specific configurations:

- **Development**: Uses `.env.development.local`
- **Production**: Uses `.env.production.local`
- **Testing**: Uses `.env.test.local`
- **Default**: Uses `.env`

## 🚀 Production Deployment

### Prerequisites

1. **Production environment files:**
   ```bash
   cp .env.example .env.production.local
   # Configure production values
   ```

2. **Build the application:**
   ```bash
   npm run build
   ```

3. **Ensure logs directory:**
   ```bash
   mkdir -p logs
   ```

### Deployment Options

#### Option 1: PM2 (Recommended)

**Benefits:**
- Process clustering for multi-core utilization
- Automatic restarts on crashes
- Zero-downtime reloads
- Built-in monitoring and logging
- Health checks and graceful shutdowns

```bash
# First-time deployment
npm run deploy:prod

# Subsequent updates (zero-downtime)
npm run pm2:reload:prod

# Monitor the application
npm run pm2:monitor
```

**PM2 Configuration Highlights:**
- **Instances**: Uses all available CPU cores
- **Memory limit**: 2GB per worker
- **Auto-restart**: Up to 5 restarts with 5s delay
- **Health monitoring**: Automated via `/health` endpoint
- **Logs**: Structured logging to `./logs/prod-*.log`

#### Option 2: Docker (Alternative)

*Note: Docker configuration not included in this template but can be added.*

#### Option 3: Direct Node.js

```bash
# Simple production start (not recommended for production)
npm run start
```

### Production Checklist

- [ ] Environment variables configured in `.env.production.local`
- [ ] Database migrations run: `npm run db:migrate:prod`
- [ ] JWT_SECRET is cryptographically secure (32+ characters)
- [ ] CORS origins configured for your domain
- [ ] Rate limiting configured appropriately
- [ ] Logs directory exists and is writable
- [ ] Health check endpoint accessible
- [ ] SSL/TLS certificate configured (external to application)
- [ ] Firewall rules configured
- [ ] Database backups configured

### Monitoring in Production

```bash
# Real-time monitoring dashboard
npm run pm2:monitor

# Check application health
curl http://localhost:3000/health

# View application logs
npm run pm2:logs:prod

# Check process status
npm run pm2:status
```

### Updating Production

1. **Pull latest changes:**
   ```bash
   git pull origin main
   ```

2. **Install new dependencies:**
   ```bash
   npm install
   ```

3. **Run migrations (if any):**
   ```bash
   npm run db:migrate:prod
   ```

4. **Build application:**
   ```bash
   npm run build
   ```

5. **Graceful reload (zero-downtime):**
   ```bash
   npm run pm2:reload:prod
   ```

### Scaling

The PM2 configuration automatically uses all available CPU cores. To customize:

```javascript
// In ecosystem.config.js
instances: 4, // Specific number of instances
// or
instances: 'max', // Use all CPU cores (default)
```

## 🔐 RBAC System Overview

This template includes a comprehensive Role-Based Access Control system that allows you to:

- Define custom roles and permissions
- Control access to resources and actions
- Set up conditional permissions based on data ownership
- Easily extend permissions for new features

### Default Roles

The system comes with four pre-defined roles:

#### SUPER_ADMIN
- **Description**: Full system access
- **Permissions**: Complete access to all resources and actions

#### ADMIN
- **Description**: Administrative access with some limitations
- **Permissions**: 
  - User management (create, read, update)
  - Profile management (read, update)
  - Product management (full access)
  - Order management (read, update, approve, reject)
  - Analytics (read, export)

#### MODERATOR
- **Description**: Content moderation and limited user management
- **Permissions**:
  - User management (read, update - excluding admins)
  - Product management (read, update, approve, reject)
  - Order viewing (read only)

#### USER
- **Description**: Standard user access
- **Permissions**:
  - Own profile management
  - Product viewing
  - Own order management

## 🛠 API Endpoints

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "role": "USER" // Optional, defaults to USER
}
```

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

Response:
```json
{
  "success": true,
  "message": "User logged in successfully",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "role": "USER",
      "createdAt": "2023-01-01T00:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### Protected Endpoints

All protected endpoints require the `Authorization: Bearer <token>` header.

#### Get User Profile
```http
GET /api/v1/auth/profile
Authorization: Bearer <token>
```

#### Update Profile
```http
PATCH /api/v1/auth/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### Change Password
```http
PATCH /api/v1/auth/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123"
}
```

#### Get User Permissions (RBAC Introspection)
```http
GET /api/v1/auth/permissions
Authorization: Bearer <token>
```

Response shows what the current user can access:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "role": "USER"
    },
    "accessibleResources": ["profile", "product", "order"],
    "permissions": {
      "profile": ["read", "update"],
      "product": ["read"],
      "order": ["create", "read", "update"]
    }
  }
}
```

### Admin Endpoints

#### Get All Users (Admin/Super Admin only)
```http
GET /api/v1/auth/users?page=1&limit=10
Authorization: Bearer <admin-token>
```

#### Update User Role (Admin only)
```http
PATCH /api/v1/auth/users/:userId/role
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "role": "MODERATOR"
}
```

## 🔒 Using RBAC in Your Code

### 1. Middleware-Based Authorization

#### Role-Based (Legacy)
```typescript
import { authMiddleware, authorizationMiddleware } from '../middlewares';
import { Role } from '@prisma/client';

// Only allow ADMIN and SUPER_ADMIN
router.get('/admin-only', 
  authMiddleware,
  authorizationMiddleware([Role.ADMIN, Role.SUPER_ADMIN]),
  controller
);
```

#### Permission-Based (Recommended)
```typescript
import { authMiddleware, permissionMiddleware } from '../middlewares';
import { Resource, Action } from '../types/rbac';

// Check if user can READ USER resources
router.get('/users', 
  authMiddleware,
  permissionMiddleware(Resource.USER, Action.READ),
  getUsersController
);

// Check with resource data (e.g., ownership)
router.patch('/profile/:userId', 
  authMiddleware,
  permissionMiddleware(
    Resource.PROFILE, 
    Action.UPDATE,
    (req) => ({ userId: req.params.userId })
  ),
  updateProfileController
);
```

#### Resource Access Check
```typescript
import { resourceAccessMiddleware } from '../middlewares';

// Check if user can access any part of USER resource
router.get('/user-dashboard', 
  authMiddleware,
  resourceAccessMiddleware(Resource.USER),
  dashboardController
);
```

### 2. Programmatic Permission Checks

```typescript
import { RBACService } from '../services/rbac.service';
import { Resource, Action } from '../types/rbac';

// In your controller
export const someController = async (req: Request, res: Response) => {
  const user = req.user!;
  
  // Check single permission
  const canUpdate = RBACService.checkPermission(
    user,
    Resource.USER,
    Action.UPDATE
  );
  
  if (!canUpdate.allowed) {
    return res.status(403).json({ 
      error: canUpdate.reason 
    });
  }
  
  // Check multiple permissions
  const permissions = RBACService.checkMultiplePermissions(user, [
    { resource: Resource.USER, action: Action.READ },
    { resource: Resource.PRODUCT, action: Action.CREATE }
  ]);
  
  // Get allowed actions for a resource
  const allowedActions = RBACService.getAllowedActions(user, Resource.ORDER);
  
  // Check resource access
  const canAccessAnalytics = RBACService.canAccessResource(user, Resource.ANALYTICS);
};
```

## 🎨 Customizing RBAC

### 1. Adding New Resources

Edit `src/types/rbac.ts`:

```typescript
export enum Resource {
  // Existing resources...
  BILLING = 'billing',
  NOTIFICATION = 'notification',
  REPORT = 'report',
}
```

### 2. Adding New Actions

```typescript
export enum Action {
  // Existing actions...
  PUBLISH = 'publish',
  ARCHIVE = 'archive',
  DUPLICATE = 'duplicate',
}
```

### 3. Creating Custom Roles

```typescript
import { RBACService } from './services/rbac.service';
import { Resource, Action } from './types/rbac';

// Create a custom role
const customRole = RBACService.createCustomRole(
  'CONTENT_MANAGER',
  'Content management specialist',
  [
    {
      resource: Resource.PRODUCT,
      actions: [Action.CREATE, Action.READ, Action.UPDATE, Action.PUBLISH],
    },
    {
      resource: Resource.ORDER,
      actions: [Action.READ],
    }
  ]
);
```

### 4. Dynamic Role Assignment

```typescript
// Update user role at runtime
await AuthService.updateUserRole(userId, 'CONTENT_MANAGER');

// Initialize RBAC with custom permissions
const customPermissions = [
  // Your custom role definitions...
];

RBACService.initialize(customPermissions);
```

### 5. Conditional Permissions

Create permissions that depend on resource data:

```typescript
const conditionalPermission = {
  resource: Resource.ORDER,
  actions: [Action.UPDATE],
  conditions: [
    {
      field: 'status',
      operator: 'eq' as const,
      value: 'pending'
    },
    {
      field: 'userId',
      operator: 'owns' as const,
      value: 'self'
    }
  ]
};
```

## 🧪 Testing the System

### 1. Create Test Users

```bash
# Register as different roles
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","role":"ADMIN"}'

curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","role":"USER"}'
```

### 2. Test Permission Endpoints

```bash
# Login to get token
TOKEN=$(curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  | jq -r '.data.token')

# Test permissions endpoint
curl -X GET http://localhost:3000/api/v1/auth/permissions \
  -H "Authorization: Bearer $TOKEN"

# Test resource access
curl -X GET http://localhost:3000/api/v1/auth/can-access-users \
  -H "Authorization: Bearer $TOKEN"
```

### 3. Test Different Permission Levels

Try accessing admin endpoints with different user roles to see the RBAC system in action.

## 📝 Best Practices

1. **Always use permission-based middleware** instead of role-based when possible
2. **Validate resource ownership** for user-specific data
3. **Use resource access middleware** for UI rendering decisions
4. **Implement proper error handling** for permission denials
5. **Log permission checks** for audit trails
6. **Keep permissions granular** but not overly complex
7. **Test all permission combinations** thoroughly

## 🚨 Security Considerations

- JWT secrets should be cryptographically secure
- Use HTTPS in production
- Implement proper rate limiting
- Validate all input data
- Log security events
- Regular security audits
- Keep dependencies updated

## 📖 Additional Resources

- [Express.js Documentation](https://expressjs.com/)
- [Prisma Documentation](https://www.prisma.io/docs/)
- [class-validator Documentation](https://github.com/typestack/class-validator)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
