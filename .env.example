# Environment
NODE_ENV=development

# Server Configuration
PORT=3000
ORIGIN=http://localhost:3000
CREDENTIALS=true

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Redis Configuration (for distributed caching, rate limiting, and token blacklist)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_CONNECTION_TIMEOUT=5000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_ATTEMPTS=3

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=30d
JWT_ISSUER=secure-backend
JWT_AUDIENCE=secure-backend-users

# JWT Key Rotation Configuration
JWT_KEY_ROTATION_ENABLED=true
JWT_KEY_ROTATION_INTERVAL_HOURS=24
JWT_KEY_RETENTION_DAYS=7

# Password Hashing Configuration
BCRYPT_SALT_ROUNDS=12

# Logging Configuration
LOG_FORMAT=dev
LOG_DIR=../logs

# Rate Limiting Configuration (Redis-based distributed)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_MESSAGE="Too many requests from this IP, please try again later."

# Distributed Rate Limiting Specific Configuration
RATE_LIMIT_LOGIN_POINTS=5
RATE_LIMIT_LOGIN_DURATION=900
RATE_LIMIT_LOGIN_BLOCK_DURATION=900
RATE_LIMIT_REGISTER_POINTS=3
RATE_LIMIT_REGISTER_DURATION=3600
RATE_LIMIT_REGISTER_BLOCK_DURATION=3600
RATE_LIMIT_PASSWORD_CHANGE_POINTS=3
RATE_LIMIT_PASSWORD_CHANGE_DURATION=3600
RATE_LIMIT_PASSWORD_CHANGE_BLOCK_DURATION=1800
RATE_LIMIT_EMAIL_VERIFICATION_POINTS=5
RATE_LIMIT_EMAIL_VERIFICATION_DURATION=3600
RATE_LIMIT_EMAIL_VERIFICATION_BLOCK_DURATION=900
RATE_LIMIT_GENERAL_API_POINTS=100
RATE_LIMIT_GENERAL_API_DURATION=60
RATE_LIMIT_GENERAL_API_BLOCK_DURATION=60
RATE_LIMIT_STRICT_POINTS=10
RATE_LIMIT_STRICT_DURATION=60
RATE_LIMIT_STRICT_BLOCK_DURATION=300

# Session Configuration
SESSION_SECRET=your_session_secret_here_make_it_long_and_random
SESSION_NAME=connect.sid
SESSION_MAX_AGE=86400000
SESSION_SECURE=false
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Session Versioning Configuration
SESSION_VERSION_TTL=86400
SESSION_CLEANUP_INTERVAL=3600000

# Email Configuration
EMAIL_VERIFICATION_TOKEN_EXPIRES=48h
MAILJET_API_KEY=your_mailjet_api_key_here
MAILJET_API_SECRET=your_mailjet_api_secret_here
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME="Your App Name"

# Security Configuration
CSRF_SECRET=your_csrf_secret_here_make_it_different_from_session_secret
CSRF_COOKIE_NAME=_csrf
CSRF_HEADER_NAME=x-csrf-token

# Input Sanitization Configuration
MAX_INPUT_LENGTH=10000
SANITIZATION_LOG_LEVEL=warn

# Token Blacklist Configuration
TOKEN_BLACKLIST_CLEANUP_INTERVAL=3600000
TOKEN_BLACKLIST_DEFAULT_TTL=604800

# Legacy Database Fields (for backward compatibility)
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=database_name
DB_USERNAME=username
DB_PASSWORD=password
SECRET_KEY=your_jwt_secret_here
