import { Router } from 'express';
import { Role } from '@prisma/client';
import { AuthController } from '../controllers/auth.controller';
import {
  authMiddleware,
  authorizationMiddleware,
  permissionMiddleware,
  resourceAccessMiddleware,
  rateLimiterMiddleware,
  validationMiddleware,
} from '../middlewares';
import { getCSRFToken } from '../middlewares/csrf.middleware';
import {
  loginRateLimit,
  registerRateLimit,
  passwordChangeRateLimit,
  emailVerificationRateLimit,
  securityHeaders,
  sanitizeInput,
  detectSuspiciousActivity,
  validatePasswordStrength,
  deviceFingerprint,
} from '../middlewares';
import { Resource, Action } from '../types/rbac';
import {
  LoginDto,
  ChangePasswordDto,
  VerifyEmailDto,
  ResendVerificationDto,
  ChangeEmailDto,
  RegisterDto,
} from '../dtos';
import { UpdateUserDto } from '../dtos/user.dto';

const router = Router();

// Apply security headers to all routes
router.use(securityHeaders);

// Apply input sanitization to all routes
router.use(sanitizeInput);

// Apply device fingerprinting to all routes
router.use(deviceFingerprint);

/**
 * PUBLIC ROUTES (No authentication required)
 */

// CSRF Token endpoint - for frontend applications
router.get('/csrf-token', getCSRFToken);

// Register - with enhanced security
router.post(
  '/register',
  detectSuspiciousActivity,
  registerRateLimit,
  validatePasswordStrength,
  validationMiddleware(RegisterDto),
  AuthController.signUp,
);

// Login - with enhanced security
router.post('/login', detectSuspiciousActivity, loginRateLimit, validationMiddleware(LoginDto), AuthController.logIn);

// Refresh token endpoint
router.post('/refresh', rateLimiterMiddleware, AuthController.refreshToken);

// Logout endpoint (public but requires refresh token)
router.post('/logout', AuthController.logOut);

/**
 * @swagger
 * /auth/verify-email:
 *   post:
 *     summary: Verify email address using verification token
 *     description: Verifies a user's email address using a token sent via email. Can be used for both initial email verification and email change confirmation.
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VerifyEmailDto'
 *     parameters:
 *       - in: query
 *         name: token
 *         schema:
 *           type: string
 *         description: Verification token (alternative to body parameter)
 *     responses:
 *       200:
 *         description: Email verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Email verified successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid or expired token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid or expired verification token
 *       429:
 *         description: Too many requests
 *         headers:
 *           Retry-After:
 *             schema:
 *               type: integer
 *             description: Seconds to wait before retrying
 */
// Verify email - public endpoint
router.post(
  '/verify-email',
  detectSuspiciousActivity,
  emailVerificationRateLimit,
  validationMiddleware(VerifyEmailDto),
  AuthController.verifyEmail,
);

/**
 * @swagger
 * /auth/resend-verification:
 *   post:
 *     summary: Resend email verification
 *     description: Resends email verification. Can be used by authenticated users or by providing an email address for unauthenticated requests.
 *     tags: [Authentication]
 *     security:
 *       - cookieAuth: []
 *       - {}
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ResendVerificationDto'
 *     responses:
 *       200:
 *         description: Verification email sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Verification email sent successfully
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Email required for unauthenticated requests
 *       404:
 *         description: User not found
 *       429:
 *         description: Rate limited - too many requests
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Please wait 5 minutes before requesting another verification email
 *                 data:
 *                   type: object
 *                   properties:
 *                     canResendAfter:
 *                       type: string
 *                       format: date-time
 *                       description: When the next resend attempt is allowed
 */
// Resend verification - flexible endpoint (authenticated or by email)
router.post(
  '/resend-verification',
  detectSuspiciousActivity,
  emailVerificationRateLimit,
  validationMiddleware(ResendVerificationDto),
  (req, res, next) => {
    // Optional authentication - proceed even if no token
    try {
      authMiddleware(req, res, err => {
        if (err) {
          // If auth fails, continue without authentication
          req.user = undefined as any;
        }
        next();
      });
    } catch {
      // If auth middleware throws, continue without authentication
      req.user = undefined as any;
      next();
    }
  },
  AuthController.resendVerification,
);

/**
 * PROTECTED ROUTES (Authentication required)
 */

// Get current user profile
router.get('/profile', authMiddleware, AuthController.getProfile);

// Update current user profile (users can only update their own profile)
router.patch(
  '/profile',
  authMiddleware,
  permissionMiddleware(Resource.PROFILE, Action.UPDATE),
  validationMiddleware(UpdateUserDto),
  AuthController.updateProfile,
);

// Change password - with enhanced security
router.patch(
  '/change-password',
  authMiddleware,
  detectSuspiciousActivity,
  passwordChangeRateLimit,
  validatePasswordStrength,
  validationMiddleware(ChangePasswordDto),
  AuthController.changePassword,
);

/**
 * @swagger
 * /auth/change-email:
 *   post:
 *     summary: Initiate email address change
 *     description: Initiates an email address change process. Sets pendingEmail and sends verification email to the new address. The email change is finalized when the verification token is used.
 *     tags: [Authentication]
 *     security:
 *       - cookieAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChangeEmailDto'
 *     responses:
 *       200:
 *         description: Email change verification sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Verification email sent to your new email address
 *                 data:
 *                   type: object
 *                   properties:
 *                     pendingEmail:
 *                       type: string
 *                       format: email
 *                       description: The new email address pending verification
 *                       example: <EMAIL>
 *                     currentEmail:
 *                       type: string
 *                       format: email
 *                       description: The current email address
 *                       example: <EMAIL>
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid request or new email same as current
 *       401:
 *         description: Authentication required
 *       409:
 *         description: Email already in use by another account
 *       429:
 *         description: Rate limited - too many requests
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Please wait 5 minutes before requesting another verification email
 *                 data:
 *                   type: object
 *                   properties:
 *                     canResendAfter:
 *                       type: string
 *                       format: date-time
 *                       description: When the next request is allowed
 */
// Change email - authenticated route
router.post(
  '/change-email',
  authMiddleware,
  detectSuspiciousActivity,
  emailVerificationRateLimit,
  validationMiddleware(ChangeEmailDto),
  AuthController.changeEmail,
);

// Get user permissions (demonstrates RBAC introspection)
router.get('/permissions', authMiddleware, AuthController.getUserPermissions);

// Check specific permission
router.get('/check-permission/:resource/:action', authMiddleware, AuthController.checkPermission);

/**
 * ADMIN ROUTES (Role-based access control)
 */

// Get all users - Legacy role-based approach
router.get('/users', authMiddleware, authorizationMiddleware([Role.ADMIN]), AuthController.getAllUsers);

// Alternative: Permission-based approach for getting users
router.get('/users-rbac', authMiddleware, permissionMiddleware(Resource.USER, Action.READ), AuthController.getAllUsers);

// Update user role - Admin only with permission check
router.patch(
  '/users/:userId/role',
  authMiddleware,
  permissionMiddleware(Resource.USER, Action.UPDATE),
  AuthController.updateUserRole,
);

// Delete user - Admin only with permission check
router.delete(
  '/users/:userId',
  authMiddleware,
  permissionMiddleware(Resource.USER, Action.DELETE),
  AuthController.deleteUser,
);

/**
 * RESOURCE ACCESS EXAMPLES
 */

// Check if user can access user management (useful for UI rendering)
router.get('/can-access-users', authMiddleware, resourceAccessMiddleware(Resource.USER), (req, res) => {
  res.json({
    success: true,
    message: 'User has access to user management',
    data: {
      resource: Resource.USER,
      user: {
        id: req.user!.id,
        role: req.user!.auth!.role,
      },
    },
  });
});

// Check if user can access analytics
router.get('/can-access-analytics', authMiddleware, resourceAccessMiddleware(Resource.ANALYTICS), (req, res) => {
  res.json({
    success: true,
    message: 'User has access to analytics',
    data: {
      resource: Resource.ANALYTICS,
      user: {
        id: req.user!.id,
        role: req.user!.auth!.role,
      },
    },
  });
});

export default router;
