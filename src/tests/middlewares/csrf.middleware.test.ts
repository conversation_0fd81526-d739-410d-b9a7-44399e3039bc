import request from 'supertest';
import express from 'express';
import session from 'express-session';
import {
  CSRFProtection,
  generateCSRFToken,
  verifyCSRFToken,
  getCSRFToken,
  sessionConfig,
} from '../../middlewares/csrf.middleware';
import { HttpException } from '../../exceptions/HttpException';

describe('CSRF Protection Middleware', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use(session(sessionConfig));
    app.use(generateCSRFToken);
    app.use(verifyCSRFToken);

    // Test routes
    app.get('/api/csrf-token', getCSRFToken);

    app.post('/api/protected', (req, res) => {
      res.json({ success: true, message: 'Protected route accessed' });
    });

    app.put('/api/protected', (req, res) => {
      res.json({ success: true, message: 'Protected route updated' });
    });

    app.delete('/api/protected', (req, res) => {
      res.json({ success: true, message: 'Protected route deleted' });
    });

    // Error handler
    app.use((err: any, req: any, res: any, next: any) => {
      if (err instanceof HttpException) {
        res.status(err.status).json({ error: err.message });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  });

  describe('CSRF Token Generation', () => {
    it('should generate CSRF token on GET request', async () => {
      const response = await request(app).get('/api/csrf-token').expect(200);

      expect(response.body.csrfToken).toBeDefined();
      expect(typeof response.body.csrfToken).toBe('string');
      expect(response.body.csrfToken.length).toBeGreaterThan(0);
    });

    it('should generate valid tokens for same session', async () => {
      const agent = request.agent(app);

      const response1 = await agent.get('/api/csrf-token').expect(200);

      const response2 = await agent.get('/api/csrf-token').expect(200);

      // Both tokens should be valid strings (they may be different for security)
      expect(response1.body.csrfToken).toBeDefined();
      expect(response2.body.csrfToken).toBeDefined();
      expect(typeof response1.body.csrfToken).toBe('string');
      expect(typeof response2.body.csrfToken).toBe('string');
    });

    it('should generate different tokens for different sessions', async () => {
      const response1 = await request(app).get('/api/csrf-token').expect(200);

      const response2 = await request(app).get('/api/csrf-token').expect(200);

      expect(response1.body.csrfToken).not.toBe(response2.body.csrfToken);
    });
  });

  describe('CSRF Token Verification', () => {
    it('should allow POST request with valid CSRF token in header', async () => {
      const agent = request.agent(app);

      // Get CSRF token
      const tokenResponse = await agent.get('/api/csrf-token').expect(200);

      const csrfToken = tokenResponse.body.csrfToken;

      // Use token in protected request
      await agent.post('/api/protected').set('x-csrf-token', csrfToken).send({ data: 'test' }).expect(200);
    });

    it('should allow POST request with valid CSRF token in body', async () => {
      const agent = request.agent(app);

      // Get CSRF token
      const tokenResponse = await agent.get('/api/csrf-token').expect(200);

      const csrfToken = tokenResponse.body.csrfToken;

      // Use token in request body
      await agent.post('/api/protected').send({ _csrf: csrfToken, data: 'test' }).expect(200);
    });

    it('should reject POST request without CSRF token', async () => {
      const agent = request.agent(app);

      // Get session first
      await agent.get('/api/csrf-token');

      // Try protected request without token
      const response = await agent.post('/api/protected').send({ data: 'test' }).expect(403);

      expect(response.body.error).toContain('CSRF token');
    });

    it('should reject POST request with invalid CSRF token', async () => {
      const agent = request.agent(app);

      // Get session first
      await agent.get('/api/csrf-token');

      // Try protected request with invalid token
      const response = await agent
        .post('/api/protected')
        .set('x-csrf-token', 'invalid-token')
        .send({ data: 'test' })
        .expect(403);

      expect(response.body.error).toContain('CSRF token');
    });

    it('should reject POST request with token from different session', async () => {
      // Get token from first session
      const tokenResponse = await request(app).get('/api/csrf-token').expect(200);

      const csrfToken = tokenResponse.body.csrfToken;

      // Try to use token in different session
      const response = await request(app)
        .post('/api/protected')
        .set('x-csrf-token', csrfToken)
        .send({ data: 'test' })
        .expect(403);

      expect(response.body.error).toContain('CSRF token');
    });
  });

  describe('HTTP Method Handling', () => {
    it('should allow GET requests without CSRF token', async () => {
      await request(app).get('/api/csrf-token').expect(200);
    });

    it('should require CSRF token for PUT requests', async () => {
      const agent = request.agent(app);

      const tokenResponse = await agent.get('/api/csrf-token').expect(200);

      const csrfToken = tokenResponse.body.csrfToken;

      await agent.put('/api/protected').set('x-csrf-token', csrfToken).send({ data: 'test' }).expect(200);
    });

    it('should require CSRF token for DELETE requests', async () => {
      const agent = request.agent(app);

      const tokenResponse = await agent.get('/api/csrf-token').expect(200);

      const csrfToken = tokenResponse.body.csrfToken;

      await agent.delete('/api/protected').set('x-csrf-token', csrfToken).expect(200);
    });
  });

  describe('CSRFProtection Class', () => {
    it('should generate valid tokens', () => {
      const mockReq = { session: { csrfSecret: 'test-secret' } } as any;
      const mockRes = { cookie: jest.fn() } as any;

      const token = CSRFProtection.generateToken(mockReq, mockRes);

      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(0);
      expect(mockRes.cookie).toHaveBeenCalled();
    });

    it('should verify valid tokens', () => {
      const mockReq = { session: { csrfSecret: 'test-secret' } } as any;
      const mockRes = { cookie: jest.fn() } as any;

      const token = CSRFProtection.generateToken(mockReq, mockRes);
      mockReq.headers = { 'x-csrf-token': token };

      const isValid = CSRFProtection.verifyToken(mockReq);
      expect(isValid).toBe(true);
    });

    it('should reject invalid tokens', () => {
      const mockReq = {
        session: { csrfSecret: 'test-secret' },
        headers: { 'x-csrf-token': 'invalid-token' },
      } as any;

      const isValid = CSRFProtection.verifyToken(mockReq);
      expect(isValid).toBe(false);
    });

    it('should handle missing session gracefully', () => {
      const mockReq = { session: null } as any;
      const mockRes = { cookie: jest.fn() } as any;

      expect(() => {
        CSRFProtection.generateToken(mockReq, mockRes);
      }).toThrow(HttpException);
    });
  });

  describe('Error Handling', () => {
    it('should handle session errors gracefully', async () => {
      const brokenApp = express();
      brokenApp.use(express.json());
      // Intentionally not setting up session middleware
      brokenApp.use(generateCSRFToken);
      brokenApp.use(verifyCSRFToken);

      brokenApp.get('/test', (req, res) => {
        res.json({ success: true });
      });

      brokenApp.use((err: any, req: any, res: any, next: any) => {
        if (err instanceof HttpException) {
          res.status(err.status).json({ error: err.message });
        } else {
          res.status(500).json({ error: 'Internal server error' });
        }
      });

      // Should return error when session is not initialized
      await request(brokenApp).get('/test').expect(500);
    });
  });
});
