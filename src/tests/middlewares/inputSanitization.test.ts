import request from 'supertest';
import express from 'express';
import { sanitizeInput } from '../../middlewares/security.middleware';
import { HttpException } from '../../exceptions/HttpException';

describe('Input Sanitization Middleware', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    app.use(sanitizeInput);

    // Test routes
    app.post('/api/test', (req, res) => {
      res.json({
        success: true,
        body: req.body,
        query: req.query,
        params: req.params,
      });
    });

    app.get('/api/test/:id', (req, res) => {
      res.json({
        success: true,
        params: req.params,
        query: req.query,
      });
    });

    // Error handler
    app.use((err: any, req: any, res: any, next: any) => {
      if (err instanceof HttpException) {
        res.status(err.status).json({ error: err.message });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  });

  describe('XSS Prevention', () => {
    it('should sanitize script tags in request body', async () => {
      const maliciousInput = {
        name: '<script>alert("xss")</script>John',
        description: 'Hello <script>alert("xss")</script> World',
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(200);

      expect(response.body.body.name).not.toContain('<script>');
      expect(response.body.body.name).not.toContain('alert');
      expect(response.body.body.description).not.toContain('<script>');
      expect(response.body.body.description).not.toContain('alert');
    });

    it('should sanitize HTML tags in request body', async () => {
      const maliciousInput = {
        content: '<img src="x" onerror="alert(1)">',
        title: '<div onclick="alert(1)">Title</div>',
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(200);

      expect(response.body.body.content).not.toContain('onerror');
      expect(response.body.body.content).not.toContain('alert');
      expect(response.body.body.title).not.toContain('onclick');
      expect(response.body.body.title).not.toContain('alert');
    });

    it('should sanitize JavaScript URLs', async () => {
      const maliciousInput = {
        link: 'javascript:alert("xss")',
        href: 'javascript:void(0)',
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(200);

      expect(response.body.body.link).not.toContain('javascript:');
      expect(response.body.body.href).not.toContain('javascript:');
    });

    it('should sanitize query parameters', async () => {
      const response = await request(app)
        .get('/api/test/123')
        .query({
          search: '<script>alert("xss")</script>',
          filter: '<img src="x" onerror="alert(1)">',
        })
        .expect(200);

      expect(response.body.query.search).not.toContain('<script>');
      expect(response.body.query.filter).not.toContain('onerror');
    });

    it('should sanitize URL parameters', async () => {
      const response = await request(app).get('/api/test/<script>alert("xss")</script>').expect(200);

      expect(response.body.params.id).not.toContain('<script>');
      expect(response.body.params.id).not.toContain('alert');
    });
  });

  describe('SQL Injection Prevention', () => {
    it('should escape SQL injection attempts in strings', async () => {
      const maliciousInput = {
        username: "admin'; DROP TABLE users; --",
        email: "<EMAIL>' OR '1'='1",
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(200);

      expect(response.body.body.username).not.toContain("'; DROP TABLE");
      expect(response.body.body.email).not.toContain("' OR '1'='1");
    });

    it('should handle UNION SELECT attacks', async () => {
      const maliciousInput = {
        id: '1 UNION SELECT * FROM users',
        search: "' UNION SELECT password FROM users WHERE '1'='1",
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(200);

      expect(response.body.body.id).not.toContain('UNION SELECT');
      expect(response.body.body.search).not.toContain('UNION SELECT');
    });
  });

  describe('Input Length Limits', () => {
    it('should reject extremely long input strings', async () => {
      const longString = 'a'.repeat(15000); // Exceeds typical limits
      const maliciousInput = {
        content: longString,
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(400);

      expect(response.body.error).toContain('Input too long');
    });

    it('should allow reasonable length inputs', async () => {
      const reasonableString = 'a'.repeat(1000);
      const validInput = {
        content: reasonableString,
      };

      const response = await request(app).post('/api/test').send(validInput).expect(200);

      expect(response.body.body.content).toBe(reasonableString);
    });
  });

  describe('Special Characters and Encoding', () => {
    it('should handle Unicode characters properly', async () => {
      const unicodeInput = {
        name: 'José María',
        emoji: '🚀 Hello 世界',
        special: 'café naïve résumé',
      };

      const response = await request(app).post('/api/test').send(unicodeInput).expect(200);

      expect(response.body.body.name).toBe('José María');
      expect(response.body.body.emoji).toContain('🚀');
      expect(response.body.body.special).toContain('café');
    });

    it('should escape HTML entities', async () => {
      const htmlInput = {
        content: 'Price: $100 & tax',
        description: 'A < B > C',
      };

      const response = await request(app).post('/api/test').send(htmlInput).expect(200);

      // Should escape HTML entities
      expect(response.body.body.content).toContain('&amp;');
      expect(response.body.body.description).toContain('&lt;');
      expect(response.body.body.description).toContain('&gt;');
    });
  });

  describe('Nested Object Sanitization', () => {
    it('should sanitize nested objects', async () => {
      const nestedInput = {
        user: {
          name: '<script>alert("xss")</script>John',
          profile: {
            bio: '<img src="x" onerror="alert(1)">',
            settings: {
              theme: '<div onclick="alert(1)">dark</div>',
            },
          },
        },
      };

      const response = await request(app).post('/api/test').send(nestedInput).expect(200);

      expect(response.body.body.user.name).not.toContain('<script>');
      expect(response.body.body.user.profile.bio).not.toContain('onerror');
      expect(response.body.body.user.profile.settings.theme).not.toContain('onclick');
    });

    it('should sanitize arrays', async () => {
      const arrayInput = {
        tags: ['<script>alert("xss")</script>', '<img src="x" onerror="alert(1)">', 'normal tag'],
        comments: [{ text: '<script>alert("xss")</script>' }, { text: 'normal comment' }],
      };

      const response = await request(app).post('/api/test').send(arrayInput).expect(200);

      expect(response.body.body.tags[0]).not.toContain('<script>');
      expect(response.body.body.tags[1]).not.toContain('onerror');
      expect(response.body.body.tags[2]).toBe('normal tag');
      expect(response.body.body.comments[0].text).not.toContain('<script>');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null and undefined values', async () => {
      const edgeCaseInput = {
        nullValue: null,
        undefinedValue: undefined,
        emptyString: '',
        zeroValue: 0,
        falseValue: false,
      };

      const response = await request(app).post('/api/test').send(edgeCaseInput).expect(200);

      expect(response.body.body.nullValue).toBeNull();
      expect(response.body.body.emptyString).toBe('');
      expect(response.body.body.zeroValue).toBe(0);
      expect(response.body.body.falseValue).toBe(false);
    });

    it('should handle circular references gracefully', async () => {
      // This test ensures the middleware doesn't crash on circular references
      // Note: Express.js typically handles this, but we test for robustness
      const response = await request(app).post('/api/test').send({ name: 'test' }).expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should preserve valid HTML entities', async () => {
      const validEntities = {
        content: '&amp; &lt; &gt; &quot; &#39;',
      };

      const response = await request(app).post('/api/test').send(validEntities).expect(200);

      // Should preserve already encoded entities
      expect(response.body.body.content).toContain('&amp;');
    });
  });

  describe('Performance and DoS Prevention', () => {
    it('should handle multiple sanitization requests efficiently', async () => {
      const requests = Array(10)
        .fill(null)
        .map(() => request(app).post('/api/test').send({ content: '<script>alert("test")</script>' }));

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.body.content).not.toContain('<script>');
      });
    });

    it('should reject deeply nested objects to prevent DoS', async () => {
      // Create deeply nested object
      let deepObject: any = { value: '<script>alert("xss")</script>' };
      for (let i = 0; i < 100; i++) {
        deepObject = { nested: deepObject };
      }

      const response = await request(app).post('/api/test').send(deepObject).expect(400);

      expect(response.body.error).toContain('too deep');
    });
  });

  describe('Content-Type Handling', () => {
    it('should sanitize form-encoded data', async () => {
      const response = await request(app)
        .post('/api/test')
        .type('form')
        .send('name=<script>alert("xss")</script>&email=<EMAIL>')
        .expect(200);

      expect(response.body.body.name).not.toContain('<script>');
      expect(response.body.body.email).toBe('<EMAIL>');
    });

    it('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/api/test')
        .type('json')
        .send('{"name": "<script>alert("xss")</script>"}') // Malformed JSON
        .expect(400);

      // Should return appropriate error for malformed JSON
      expect(response.status).toBe(400);
    });
  });
});
