import { SessionVersioningService } from '../../services/sessionVersioning.service';
import { redis } from '../../config/redis';
import { logger } from '../../utils/logger';
import { Role } from '@prisma/client';

// Mock Redis
jest.mock('../../config/redis', () => ({
  redis: {
    incr: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
    expire: jest.fn(),
    hgetall: jest.fn(),
    pipeline: jest.fn(() => ({
      del: jest.fn(),
      exec: jest.fn(),
    })),
  },
}));

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe('SessionVersioningService', () => {
  const mockRedis = redis as jest.Mocked<typeof redis>;
  const mockLogger = logger as jest.Mocked<typeof logger>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // 2022-01-01 00:00:00

    // Set up default mock implementations
    mockRedis.expire.mockResolvedValue(1);
    mockRedis.hgetall.mockResolvedValue({});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('incrementUserVersion', () => {
    it('should increment user version and invalidate sessions', async () => {
      const userId = 'user-123';
      const reason = 'Role changed';

      mockRedis.incr.mockResolvedValue(2);
      mockRedis.keys.mockResolvedValue(['session:user-123:session1', 'session:user-123:session2']);
      mockRedis.pipeline.mockReturnValue({
        del: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
      } as any);

      const newVersion = await SessionVersioningService.incrementUserVersion(userId, reason);

      expect(newVersion).toBe(2);
      expect(mockRedis.incr).toHaveBeenCalledWith('session_version:user-123');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'User session version incremented',
        expect.objectContaining({
          userId,
          newVersion: 2,
          reason,
        }),
      );
    });

    it('should handle Redis errors during version increment', async () => {
      const userId = 'user-123';
      mockRedis.incr.mockRejectedValue(new Error('Redis connection failed'));

      await expect(SessionVersioningService.incrementUserVersion(userId, 'test')).rejects.toThrow(
        'Redis connection failed',
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to increment user session version',
        expect.objectContaining({
          userId,
          error: 'Redis connection failed',
        }),
      );
    });
  });

  describe('getUserVersion', () => {
    it('should return user version when it exists', async () => {
      const userId = 'user-123';
      mockRedis.get.mockResolvedValue('5');

      const version = await SessionVersioningService.getUserVersion(userId);

      expect(version).toBe(5);
      expect(mockRedis.get).toHaveBeenCalledWith('session_version:user-123');
    });

    it('should return 0 for new users without version', async () => {
      const userId = 'new-user';
      mockRedis.get.mockResolvedValue(null);

      const version = await SessionVersioningService.getUserVersion(userId);

      expect(version).toBe(0);
    });

    it('should handle Redis errors gracefully', async () => {
      const userId = 'user-123';
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      const version = await SessionVersioningService.getUserVersion(userId);

      expect(version).toBe(0);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get user session version',
        expect.objectContaining({
          userId,
          error: 'Redis connection failed',
        }),
      );
    });

    it('should handle invalid version data', async () => {
      const userId = 'user-123';
      mockRedis.get.mockResolvedValue('invalid-number');

      const version = await SessionVersioningService.getUserVersion(userId);

      expect(version).toBe(0);
    });
  });

  describe('validateSessionVersion', () => {
    it('should return true for valid session version', async () => {
      const mockReq = {
        session: {
          userId: 'user-123',
          sessionVersion: 3,
        },
      } as any;

      mockRedis.get.mockResolvedValue('3');

      const isValid = await SessionVersioningService.validateSessionVersion(mockReq);

      expect(isValid).toBe(true);
    });

    it('should return false for outdated session version', async () => {
      const mockReq = {
        session: {
          userId: 'user-123',
          sessionVersion: 2,
        },
      } as any;

      mockRedis.get.mockResolvedValue('3');

      const isValid = await SessionVersioningService.validateSessionVersion(mockReq);

      expect(isValid).toBe(false);
    });

    it('should return false for missing session data', async () => {
      const mockReq = { session: {} } as any;

      const isValid = await SessionVersioningService.validateSessionVersion(mockReq);

      expect(isValid).toBe(false);
    });

    it('should return false for missing session', async () => {
      const mockReq = {} as any;

      const isValid = await SessionVersioningService.validateSessionVersion(mockReq);

      expect(isValid).toBe(false);
    });

    it('should handle Redis errors and return false', async () => {
      const mockReq = {
        session: {
          userId: 'user-123',
          sessionVersion: 3,
        },
      } as any;

      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      const isValid = await SessionVersioningService.validateSessionVersion(mockReq);

      expect(isValid).toBe(false);
    });
  });

  describe('initializeSession', () => {
    it('should initialize session with current user version', async () => {
      const mockReq = { session: {} } as any;
      const mockUser = {
        id: 'user-123',
        role: Role.USER,
        username: null,
        name: null,
        email: '<EMAIL>',
        emailVerified: null,
        image: null,
        passwordHash: 'hashed',
        failedLoginAttempts: 0,
        lockoutExpires: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRedis.get.mockResolvedValue('2');

      await SessionVersioningService.initializeSession(mockReq, mockUser);

      expect(mockReq.session.userId).toBe('user-123');
      expect(mockReq.session.sessionVersion).toBe(2);
      expect(mockReq.session.userRole).toBe(Role.USER);
      expect(mockReq.session.sessionCreatedAt).toBeDefined();
    });

    it('should initialize session with version 0 for new users', async () => {
      const mockReq = { session: {} } as any;
      const mockUser = {
        id: 'new-user',
        role: Role.USER,
        username: null,
        name: null,
        email: '<EMAIL>',
        emailVerified: null,
        image: null,
        passwordHash: 'hashed',
        failedLoginAttempts: 0,
        lockoutExpires: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRedis.get.mockResolvedValue(null);

      await SessionVersioningService.initializeSession(mockReq, mockUser);

      expect(mockReq.session.sessionVersion).toBe(0);
    });

    it('should handle missing session gracefully', async () => {
      const mockReq = {} as any;
      const mockUser = {
        id: 'user-123',
        role: Role.USER,
        username: null,
        name: null,
        email: '<EMAIL>',
        emailVerified: null,
        image: null,
        passwordHash: 'hashed',
        failedLoginAttempts: 0,
        lockoutExpires: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await expect(SessionVersioningService.initializeSession(mockReq, mockUser)).resolves.not.toThrow();
    });
  });

  describe('handleRoleChange', () => {
    it('should increment version and log role change', async () => {
      const userId = 'user-123';
      const oldRole = 'user';
      const newRole = 'admin';

      mockRedis.incr.mockResolvedValue(3);
      mockRedis.keys.mockResolvedValue([]);
      mockRedis.pipeline.mockReturnValue({
        del: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
      } as any);

      await SessionVersioningService.handleRoleChange(userId, oldRole, newRole);

      expect(mockRedis.incr).toHaveBeenCalledWith('session_version:user-123');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'User session version incremented',
        expect.objectContaining({
          userId,
          reason: `Role changed from ${oldRole} to ${newRole}`,
        }),
      );
    });

    it('should not increment version if roles are the same', async () => {
      const userId = 'user-123';
      const role = 'user';

      await SessionVersioningService.handleRoleChange(userId, role, role);

      expect(mockRedis.incr).not.toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Role change detected but roles are identical, skipping version increment',
        { userId, role },
      );
    });
  });

  describe('handlePasswordChange', () => {
    it('should increment version for password change', async () => {
      const userId = 'user-123';

      mockRedis.incr.mockResolvedValue(4);
      mockRedis.keys.mockResolvedValue([]);
      mockRedis.pipeline.mockReturnValue({
        del: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
      } as any);

      await SessionVersioningService.handlePasswordChange(userId);

      expect(mockRedis.incr).toHaveBeenCalledWith('session_version:user-123');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'User session version incremented',
        expect.objectContaining({
          userId,
          reason: 'Password changed',
        }),
      );
    });
  });

  describe('invalidateSession', () => {
    it('should destroy session and clear cookie', async () => {
      const mockReq = {
        session: {
          destroy: jest.fn(callback => callback()),
        },
      } as any;
      const mockRes = {
        clearCookie: jest.fn(),
      } as any;

      await SessionVersioningService.invalidateSession(mockReq, mockRes);

      expect(mockReq.session.destroy).toHaveBeenCalled();
      expect(mockRes.clearCookie).toHaveBeenCalledWith('connect.sid');
    });

    it('should handle session destruction errors', async () => {
      const mockReq = {
        session: {
          destroy: jest.fn(callback => callback(new Error('Destroy failed'))),
        },
      } as any;
      const mockRes = {
        clearCookie: jest.fn(),
      } as any;

      await expect(SessionVersioningService.invalidateSession(mockReq, mockRes)).rejects.toThrow('Destroy failed');
    });

    it('should handle missing session gracefully', async () => {
      const mockReq = {} as any;
      const mockRes = { clearCookie: jest.fn() } as any;

      await expect(SessionVersioningService.invalidateSession(mockReq, mockRes)).resolves.not.toThrow();

      expect(mockRes.clearCookie).toHaveBeenCalled();
    });
  });

  describe('invalidateAllUserSessions', () => {
    it('should invalidate all sessions for a user', async () => {
      const userId = 'user-123';
      const mockSessions = ['session:user-123:sess1', 'session:user-123:sess2'];
      const mockPipeline = {
        del: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
      };

      mockRedis.keys.mockResolvedValue(mockSessions);
      mockRedis.pipeline.mockReturnValue(mockPipeline as any);

      const result = await SessionVersioningService.invalidateAllUserSessions(userId);

      expect(result.invalidated).toBe(2);
      expect(mockRedis.keys).toHaveBeenCalledWith('session:user-123:*');
      expect(mockPipeline.del).toHaveBeenCalledTimes(2);
      expect(mockPipeline.exec).toHaveBeenCalled();
    });

    it('should handle no sessions gracefully', async () => {
      const userId = 'user-123';
      mockRedis.keys.mockResolvedValue([]);

      const result = await SessionVersioningService.invalidateAllUserSessions(userId);

      expect(result.invalidated).toBe(0);
    });

    it('should handle Redis errors', async () => {
      const userId = 'user-123';
      mockRedis.keys.mockRejectedValue(new Error('Redis connection failed'));

      const result = await SessionVersioningService.invalidateAllUserSessions(userId);

      expect(result.invalidated).toBe(0);
      expect(result.error).toBe('Failed to invalidate user sessions');
    });
  });

  describe('getSessionStats', () => {
    it('should return session statistics', async () => {
      const userId = 'user-123';
      mockRedis.get.mockResolvedValue('3');
      mockRedis.keys.mockResolvedValue(['session:user-123:sess1', 'session:user-123:sess2']);

      const stats = await SessionVersioningService.getSessionStats(userId);

      expect(stats).toEqual({
        userId,
        currentVersion: 3,
        activeSessions: 2,
        timestamp: expect.any(String),
      });
    });

    it('should handle errors in stats collection', async () => {
      const userId = 'user-123';
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      const stats = await SessionVersioningService.getSessionStats(userId);

      expect(stats).toEqual({
        userId,
        currentVersion: 0,
        activeSessions: 0,
        timestamp: expect.any(String),
        error: 'Failed to retrieve session statistics',
      });
    });
  });
});
