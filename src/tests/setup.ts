import 'reflect-metadata';
import dotenv from 'dotenv';
import path from 'path';

// Load test environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.test.local') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

// Set default test environment variables
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
process.env.JWT_EXPIRES_IN = '1h';
process.env.BCRYPT_SALT_ROUNDS = '4'; // Lower for faster tests
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/test_db';
process.env.LOG_LEVEL = 'error'; // Reduce logging noise in tests
process.env.RATE_LIMIT_MAX = '1000'; // Higher limit for tests
process.env.RATE_LIMIT_WINDOW_MS = '60000';
process.env.ORIGIN = 'http://localhost:3001';
process.env.CREDENTIALS = 'true';

// Email service test environment variables
process.env.MAILJET_API_KEY = 'test_mailjet_api_key';
process.env.MAILJET_API_SECRET = 'test_mailjet_api_secret';
process.env.MAIL_FROM = '<EMAIL>';
process.env.MAIL_FROM_NAME = 'Test App';
process.env.EMAIL_VERIFICATION_TOKEN_EXPIRES = '48h';

// Global test setup
beforeAll(async () => {
  // Setup database connection or mock services
  // Initialize test database if needed
});

afterAll(async () => {
  // Cleanup resources
  // Close database connections
  // Clear test data
});

// Mock Mailjet globally for all tests
jest.mock('node-mailjet', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      post: jest.fn().mockReturnValue({
        request: jest.fn().mockResolvedValue({
          body: {
            Messages: [{ MessageID: 'test-message-id-123' }],
          },
        }),
      }),
      get: jest.fn().mockReturnValue({
        request: jest.fn().mockResolvedValue({ body: { Data: [] } }),
      }),
    })),
  };
});

// Global test utilities
beforeEach(() => {
  // Reset any mocks or test state
  jest.clearAllMocks();
});

// Suppress console logs during tests unless LOG_LEVEL=debug
if (process.env.LOG_LEVEL !== 'debug') {
  global.console = {
    ...console,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
}
