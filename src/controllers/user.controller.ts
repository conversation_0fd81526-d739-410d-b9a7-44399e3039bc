import { Request, Response } from 'express';
import { Container } from 'typedi';
import { Role } from '@prisma/client';
import { BaseController } from './base.controller';
import { UserService } from '../services/user.service';
import { CreateUserDto, UpdateUserDto } from '../dtos/user.dto';
import { HttpException } from '../exceptions/HttpException';
import { UserFilters } from '../repositories/user.repository';

export class UserController extends BaseController {
  private userService: UserService;

  constructor() {
    super();
    this.userService = Container.get(UserService);
  }

  public getUsers = this.catchAsync(async (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const filters: UserFilters = {};
    if (req.query.email) filters.email = req.query.email as string;
    if (req.query.role) filters.role = req.query.role as Role;
    if (req.query.username) filters.username = req.query.username as string;

    const result = await this.userService.findAllUser(filters, page, limit);
    this.sendResponse(res, 200, result);
  });

  public getUserById = this.catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    if (!id) {
      throw new HttpException(400, 'User ID is required');
    }
    const user = await this.userService.findUserById(id);
    this.sendResponse(res, 200, user);
  });

  public createUser = this.catchAsync(async (req: Request, res: Response) => {
    const user = await this.userService.createUser(req.body as CreateUserDto);
    this.sendResponse(res, 201, user, 'User created successfully');
  });

  public updateUser = this.catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    if (!id) {
      throw new HttpException(400, 'User ID is required');
    }
    const requestingUser = req.user;

    if (requestingUser?.id !== id && requestingUser?.auth?.role !== Role.ADMIN) {
      throw new HttpException(403, 'Forbidden');
    }

    const user = await this.userService.updateUser(id, req.body as UpdateUserDto);
    this.sendResponse(res, 200, user, 'User updated successfully');
  });

  public deleteUser = this.catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    if (!id) {
      throw new HttpException(400, 'User ID is required');
    }
    const requestingUser = req.user;

    if (requestingUser?.id !== id && requestingUser?.auth?.role !== Role.ADMIN) {
      throw new HttpException(403, 'Forbidden');
    }
    await this.userService.deleteUser(id);
    this.sendResponse(res, 204, null, 'User deleted successfully');
  });
}
