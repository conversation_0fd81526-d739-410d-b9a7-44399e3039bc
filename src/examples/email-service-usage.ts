/**
 * Email Service Usage Examples
 *
 * This file demonstrates how to integrate and use the EmailService
 * in your application for sending verification emails.
 */

import { EmailService, createEmailService } from '../services/email.service';
import { BaseUserData } from '../types/template';

// Example 1: Basic setup and email verification
export async function sendUserVerificationEmail() {
  // Initialize email service
  const emailService = createEmailService();

  // Mock user data (replace with actual user from your database)
  const user: BaseUserData = {
    id: 'user-12345',
    email: '<EMAIL>',
    role: 'USER',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Generate verification token (replace with your JWT generation logic)
  const verificationToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';

  try {
    // Send verification email
    const result = await emailService.sendVerificationEmail(user, verificationToken);

    if (result.success) {
      console.log('✅ Verification email sent successfully');
      console.log(`📧 Message ID: ${result.messageId}`);
      return { success: true, messageId: result.messageId };
    } else {
      console.error('❌ Failed to send verification email:', result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error('💥 Error sending verification email:', error);
    return { success: false, error: error.message };
  }
}

// Example 2: Email change verification
export async function sendEmailChangeVerification() {
  const emailService = createEmailService();

  // User with new email address
  const user: BaseUserData = {
    id: 'user-12345',
    email: '<EMAIL>', // New email address
    role: 'USER',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const verificationToken = 'change-email-token-xyz';

  try {
    // Send email change verification (note the 'true' parameter)
    const result = await emailService.sendVerificationEmail(
      user,
      verificationToken,
      true, // isEmailChange = true
    );

    if (result.success) {
      console.log('✅ Email change verification sent successfully');
      return { success: true, messageId: result.messageId };
    } else {
      console.error('❌ Failed to send email change verification:', result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error('💥 Error sending email change verification:', error);
    return { success: false, error: error.message };
  }
}

// Example 3: Advanced usage with custom options
export async function sendVerificationWithCustomOptions() {
  const emailService = createEmailService();

  const user: BaseUserData = {
    id: 'user-12345',
    email: '<EMAIL>',
    role: 'PREMIUM',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const verificationToken = 'premium-user-token';

  try {
    // Send with custom retry and tracking options
    const result = await emailService.sendVerificationEmail(
      user,
      verificationToken,
      false, // Not an email change
      {
        retries: 5, // Retry up to 5 times
        trackOpens: true, // Track email opens
        trackClicks: true, // Track link clicks
      },
    );

    if (result.success) {
      console.log('✅ Premium user verification email sent with custom options');
      return { success: true, messageId: result.messageId };
    } else {
      console.error('❌ Failed to send premium verification email:', result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error('💥 Error sending premium verification email:', error);
    return { success: false, error: error.message };
  }
}

// Example 4: Health check integration
export async function checkEmailServiceHealth() {
  const emailService = createEmailService();

  try {
    const isHealthy = await emailService.testConnection();

    if (isHealthy) {
      console.log('✅ Email service is healthy');
      return { healthy: true };
    } else {
      console.warn('⚠️ Email service health check failed');
      return { healthy: false };
    }
  } catch (error) {
    console.error('💥 Email service health check error:', error);
    return { healthy: false, error: error.message };
  }
}

// Example 5: Template cache management
export async function manageTemplateCache() {
  const emailService = createEmailService();

  try {
    // Get cache statistics
    const stats = emailService.getTemplateCacheStats();
    console.log('📊 Template cache stats:', stats);

    // Clear cache if needed (useful during development)
    if (process.env.NODE_ENV === 'development') {
      emailService.clearTemplateCache();
      console.log('🧹 Template cache cleared for development');
    }

    return stats;
  } catch (error) {
    console.error('💥 Error managing template cache:', error);
    return null;
  }
}

// Example 6: Integration with Express.js route
export function setupEmailRoutes(app: any) {
  // Route to send verification email
  app.post('/api/auth/send-verification', async (req, res) => {
    try {
      const { userId, email, token } = req.body;

      if (!userId || !email || !token) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: userId, email, token',
        });
      }

      const user: BaseUserData = {
        id: userId,
        email: email,
        role: 'USER',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const emailService = createEmailService();
      const result = await emailService.sendVerificationEmail(user, token);

      res.json({
        success: result.success,
        messageId: result.messageId,
        error: result.error,
      });
    } catch (error) {
      console.error('API Error sending verification email:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Health check route
  app.get('/api/health/email', async (req, res) => {
    try {
      const emailService = createEmailService();
      const isHealthy = await emailService.testConnection();

      res.status(isHealthy ? 200 : 503).json({
        service: 'email',
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Health check error:', error);
      res.status(503).json({
        service: 'email',
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  });
}

// Example usage in application startup
export async function initializeEmailService() {
  try {
    // Validate configuration before starting
    EmailService.validateConfiguration();
    console.log('✅ Email service configuration validated');

    // Test connection
    const emailService = createEmailService();
    const isHealthy = await emailService.testConnection();

    if (isHealthy) {
      console.log('✅ Email service connection test passed');
    } else {
      console.warn('⚠️ Email service connection test failed');
      // You might want to continue anyway or exit depending on your requirements
    }

    return true;
  } catch (error) {
    console.error('💥 Failed to initialize email service:', error);

    // In production, you might want to exit the process
    if (process.env.NODE_ENV === 'production') {
      console.error('🚨 Email service is required in production. Exiting...');
      process.exit(1);
    }

    return false;
  }
}

// Export all examples for easy testing
export const EmailServiceExamples = {
  sendUserVerificationEmail,
  sendEmailChangeVerification,
  sendVerificationWithCustomOptions,
  checkEmailServiceHealth,
  manageTemplateCache,
  setupEmailRoutes,
  initializeEmailService,
};
