import Redis from 'ioredis';
import { logger } from '../utils/secureLogger';

/**
 * Redis Configuration and Connection Management
 * Provides centralized Redis connection with proper error handling and reconnection logic
 */
export class RedisConfig {
  private static instance: Redis | null = null;
  private static isConnected = false;

  /**
   * Get Redis instance with connection management
   */
  static getInstance(): Redis {
    if (!this.instance) {
      this.instance = this.createConnection();
    }
    return this.instance;
  }

  /**
   * Create Redis connection with proper configuration
   */
  private static createConnection(): Redis {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    
    const redis = new Redis(redisUrl, {
      // Connection options
      connectTimeout: 10000,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,
      
      // Reconnection strategy
      retryStrategy: (times: number) => {
        const delay = Math.min(times * 50, 2000);
        logger.warn(`Redis reconnection attempt ${times}, delay: ${delay}ms`);
        return delay;
      },

      // Connection pool settings
      family: 4,
      keepAlive: 30000, // 30 seconds
      
      // Key prefix for this application
      keyPrefix: 'secure_backend:',
    });

    // Connection event handlers
    redis.on('connect', () => {
      logger.info('Redis connection established');
      this.isConnected = true;
    });

    redis.on('ready', () => {
      logger.info('Redis connection ready');
    });

    redis.on('error', (error) => {
      logger.error('Redis connection error', { error: error.message });
      this.isConnected = false;
    });

    redis.on('close', () => {
      logger.warn('Redis connection closed');
      this.isConnected = false;
    });

    redis.on('reconnecting', () => {
      logger.info('Redis reconnecting...');
    });

    return redis;
  }

  /**
   * Check if Redis is connected and healthy
   */
  static async isHealthy(): Promise<boolean> {
    try {
      if (!this.instance || !this.isConnected) {
        return false;
      }
      
      const result = await this.instance.ping();
      return result === 'PONG';
    } catch (error) {
      logger.error('Redis health check failed', { error });
      return false;
    }
  }

  /**
   * Gracefully close Redis connection
   */
  static async close(): Promise<void> {
    if (this.instance) {
      try {
        await this.instance.quit();
        logger.info('Redis connection closed gracefully');
      } catch (error) {
        logger.error('Error closing Redis connection', { error });
      } finally {
        this.instance = null;
        this.isConnected = false;
      }
    }
  }

  /**
   * Get connection status
   */
  static getConnectionStatus(): { connected: boolean; instance: boolean } {
    return {
      connected: this.isConnected,
      instance: !!this.instance
    };
  }
}

// Export singleton instance
export const redis = RedisConfig.getInstance();
export default RedisConfig;
