import { config } from 'dotenv';
import { cleanEnv, str, port, bool, num, url } from 'envalid';

// Load environment-specific dotenv file
config({ path: `.env.${process.env.NODE_ENV || 'development'}.local` });

// Validate and create typed environment object
export const env = cleanEnv(
  process.env,
  {
    // Environment
    NODE_ENV: str({ choices: ['development', 'test', 'production'], default: 'development' }),

    // Server Configuration
    PORT: port({ default: 3000 }),
    ORIGIN: str({ default: '*' }),
    CREDENTIALS: bool({ default: true }),

    // Database
    DATABASE_URL: url(),

    // JWT Configuration
    JWT_SECRET: str({ desc: 'Secret key for JWT token generation' }),
    JWT_EXPIRES_IN: str({ default: '15m', desc: 'JWT access token expiration time' }),
    JWT_REFRESH_EXPIRES_IN: str({ default: '30d', desc: 'JWT refresh token expiration time' }),

    // Password Hashing Configuration
    BCRYPT_SALT_ROUNDS: num({ default: 12, desc: 'Number of salt rounds for bcrypt password hashing' }),

    // Logging Configuration
    LOG_FORMAT: str({ choices: ['dev', 'combined', 'common', 'short', 'tiny'], default: 'dev' }),
    LOG_DIR: str({ default: '../logs', desc: 'Directory for log files' }),

    // Rate Limiting Configuration
    RATE_LIMIT_WINDOW_MS: num({ default: 15 * 60 * 1000, desc: 'Rate limit window in milliseconds (15 minutes)' }),
    RATE_LIMIT_MAX: num({ default: 100, desc: 'Maximum requests per window' }),
    RATE_LIMIT_MESSAGE: str({
      default: 'Too many requests from this IP, please try again later.',
      desc: 'Rate limit exceeded message',
    }),

    // Email Configuration
    EMAIL_VERIFICATION_TOKEN_EXPIRES: str({ default: '48h', desc: 'Email verification token expiration time' }),
    MAILJET_API_KEY: str({ desc: 'Mailjet API key for sending emails' }),
    MAILJET_API_SECRET: str({ desc: 'Mailjet API secret for authentication' }),
    MAIL_FROM: str({ desc: 'From email address for outgoing emails' }),
    MAIL_FROM_NAME: str({ desc: 'Display name for outgoing emails' }),

    // Legacy Database Fields (for backward compatibility)
    DB_HOST: str({ default: 'localhost' }),
    DB_PORT: port({ default: 5432 }),
    DB_DATABASE: str({ default: 'database_name' }),
    DB_USERNAME: str({ default: 'username' }),
    DB_PASSWORD: str({ default: 'password' }),
    SECRET_KEY: str({ default: 'fallback_secret' }),
  },
  {
    reporter: ({ errors, env }) => {
      if (Object.keys(errors).length > 0) {
        console.error('❌ Invalid environment variables:');
        Object.entries(errors).forEach(([key, error]) => {
          console.error(`  ${key}: ${error.message}`);
        });
        process.exit(1);
      }
      console.log(`✅ Environment validation passed for ${(env as any).NODE_ENV} mode`);
    },
  },
);

// Export individual variables for backward compatibility
export const {
  NODE_ENV,
  PORT,
  ORIGIN,
  CREDENTIALS,
  DATABASE_URL,
  JWT_SECRET,
  JWT_EXPIRES_IN,
  JWT_REFRESH_EXPIRES_IN,
  BCRYPT_SALT_ROUNDS,
  LOG_FORMAT,
  LOG_DIR,
  RATE_LIMIT_WINDOW_MS,
  RATE_LIMIT_MAX,
  RATE_LIMIT_MESSAGE,
  EMAIL_VERIFICATION_TOKEN_EXPIRES,
  MAILJET_API_KEY,
  MAILJET_API_SECRET,
  MAIL_FROM,
  MAIL_FROM_NAME,
  DB_HOST,
  DB_PORT,
  DB_DATABASE,
  DB_USERNAME,
  DB_PASSWORD,
  SECRET_KEY,
} = env;
