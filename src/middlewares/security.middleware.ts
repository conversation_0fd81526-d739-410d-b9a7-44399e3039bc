import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import DOMPurify from 'isomorphic-dompurify';
import validator from 'validator';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/logger';
import { LoggingContext } from '../types/template';
import { redis } from '../config/redis';

// Redis-based distributed rate limiting for different endpoints
const loginLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_login',
  points: 5, // Number of attempts
  duration: 15 * 60, // Per 15 minutes
  blockDuration: 15 * 60, // Block for 15 minutes
  execEvenly: true, // Spread requests evenly across duration
});

const registerLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_register',
  points: 3, // Number of registration attempts
  duration: 60 * 60, // Per hour
  blockDuration: 60 * 60, // Block for 1 hour
  execEvenly: true,
});

const passwordChangeLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_password_change',
  points: 3, // Number of password change attempts
  duration: 60 * 60, // Per hour
  blockDuration: 30 * 60, // Block for 30 minutes
  execEvenly: true,
});

const emailVerificationLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_email_verification',
  points: 5, // Number of email verification requests
  duration: 60 * 60, // Per hour
  blockDuration: 15 * 60, // Block for 15 minutes
  execEvenly: true,
});

// General API rate limiter for all endpoints
const generalApiLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_api_general',
  points: 100, // Number of requests
  duration: 60, // Per minute
  blockDuration: 60, // Block for 1 minute
  execEvenly: true,
});

// Strict rate limiter for sensitive operations
const strictLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_strict',
  points: 10, // Number of requests
  duration: 60, // Per minute
  blockDuration: 5 * 60, // Block for 5 minutes
  execEvenly: true,
});

/**
 * Enhanced login rate limiter with progressive delays
 */
export const loginRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = `${req.ip || 'unknown'}_${req.body?.email || 'unknown'}`;
    await loginLimiter.consume(key);
    next();
  } catch (rateLimiterRes: any) {
    const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

    logger.warn('Login rate limit exceeded', {
      ip: req.ip || 'unknown',
      email: req.body?.email || 'unknown',
      remainingPoints: rateLimiterRes.remainingPoints || 0,
      msBeforeNext: rateLimiterRes.msBeforeNext || 0,
    } as LoggingContext);

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many login attempts. Try again in ${secs} seconds.`));
  }
};

/**
 * Registration rate limiter
 */
export const registerRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = req.ip || 'unknown';
    await registerLimiter.consume(key);
    next();
  } catch (rateLimiterRes: any) {
    const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

    logger.warn('Registration rate limit exceeded', {
      ip: req.ip || 'unknown',
      remainingPoints: rateLimiterRes.remainingPoints || 0,
    } as LoggingContext);

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many registration attempts. Try again in ${secs} seconds.`));
  }
};

/**
 * Password change rate limiter
 */
export const passwordChangeRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = `${req.ip || 'unknown'}_${req.user?.id || 'unknown'}`;
    await passwordChangeLimiter.consume(key);
    next();
  } catch (rateLimiterRes: any) {
    const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

    logger.warn('Password change rate limit exceeded', {
      ip: req.ip || 'unknown',
      userId: req.user?.id || 'unknown',
      remainingPoints: rateLimiterRes.remainingPoints || 0,
    } as LoggingContext);

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many password change attempts. Try again in ${secs} seconds.`));
  }
};

/**
 * Email verification rate limiter
 */
export const emailVerificationRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Use IP and email/userId for rate limiting key
    const email = req.body?.email || req.user?.auth?.email || 'unknown';
    const key = `${req.ip || 'unknown'}_${email}`;
    await emailVerificationLimiter.consume(key);
    next();
  } catch (rateLimiterRes: any) {
    const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

    logger.warn('Email verification rate limit exceeded', {
      ip: req.ip || 'unknown',
      email: req.body?.email || req.user?.auth?.email || 'unknown',
      remainingPoints: rateLimiterRes.remainingPoints || 0,
    } as LoggingContext);

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many email verification requests. Try again in ${secs} seconds.`));
  }
};

/**
 * Security headers middleware
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Remove potentially sensitive headers
  res.removeHeader('X-Powered-By');

  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  next();
};

/**
 * Enhanced input sanitization middleware using DOMPurify and validator.js
 * Provides comprehensive protection against XSS, injection attacks, and malicious input
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  try {
    const sanitizeString = (value: string): string => {
      if (typeof value !== 'string') {
        return value;
      }

      // Trim whitespace
      let sanitized = value.trim();

      // Limit string length to prevent DoS attacks
      if (sanitized.length > 10000) {
        sanitized = sanitized.substring(0, 10000);
        logger.warn('Input truncated due to excessive length', {
          originalLength: value.length,
          truncatedLength: sanitized.length,
          ip: req.ip,
        });
      }

      // HTML escape using validator.js
      sanitized = validator.escape(sanitized);

      // Additional XSS protection using DOMPurify
      sanitized = DOMPurify.sanitize(sanitized, {
        ALLOWED_TAGS: [], // No HTML tags allowed
        ALLOWED_ATTR: [], // No attributes allowed
        KEEP_CONTENT: true, // Keep text content
        RETURN_DOM: false,
        RETURN_DOM_FRAGMENT: false,
      });

      // Additional security checks
      if (sanitized !== value) {
        logger.warn('Potentially malicious input detected and sanitized', {
          originalLength: value.length,
          sanitizedLength: sanitized.length,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.path,
        });
      }

      return sanitized;
    };

    const sanitizeObject = (obj: any): any => {
      if (obj === null || obj === undefined) {
        return obj;
      }

      if (typeof obj === 'string') {
        return sanitizeString(obj);
      }

      if (typeof obj === 'number' || typeof obj === 'boolean') {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      if (typeof obj === 'object') {
        const sanitized: any = {};
        for (const [key, value] of Object.entries(obj)) {
          // Sanitize both key and value
          const sanitizedKey = sanitizeString(key);
          sanitized[sanitizedKey] = sanitizeObject(value);
        }
        return sanitized;
      }

      return obj;
    };

    // Sanitize request body
    if (req.body) {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query) {
      const sanitizedQuery: any = {};
      Object.entries(req.query).forEach(([key, value]) => {
        const sanitizedKey = sanitizeString(key);
        sanitizedQuery[sanitizedKey] = sanitizeObject(value);
      });
      req.query = sanitizedQuery;
    }

    // Sanitize URL parameters
    if (req.params) {
      const sanitizedParams: any = {};
      Object.entries(req.params).forEach(([key, value]) => {
        const sanitizedKey = sanitizeString(key);
        sanitizedParams[sanitizedKey] = sanitizeString(value as string);
      });
      req.params = sanitizedParams;
    }

    next();
  } catch (error) {
    logger.error('Input sanitization failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
      path: req.path,
      method: req.method,
    });

    // Fail secure - reject the request if sanitization fails
    next(new HttpException(400, 'Invalid input format'));
  }
};

/**
 * Account lockout tracking
 */
const accountLockouts = new Map<string, { attempts: number; lockedUntil?: Date }>();

export const trackFailedLogin = (email: string) => {
  const current = accountLockouts.get(email) || { attempts: 0 };
  current.attempts += 1;

  // Lock account after 5 failed attempts for 30 minutes
  if (current.attempts >= 5) {
    current.lockedUntil = new Date(Date.now() + 30 * 60 * 1000);
    logger.warn('Account locked due to failed login attempts', {
      email,
      attempts: current.attempts,
      lockedUntil: current.lockedUntil,
    } as LoggingContext);
  }

  accountLockouts.set(email, current);
};

export const trackSuccessfulLogin = (email: string) => {
  // Clear failed attempts on successful login
  accountLockouts.delete(email);
};

export const checkAccountLockout = (email: string): boolean => {
  const account = accountLockouts.get(email);
  if (!account || !account.lockedUntil) {
    return false;
  }

  // Check if lockout has expired
  if (new Date() > account.lockedUntil) {
    accountLockouts.delete(email);
    return false;
  }

  return true;
};

/**
 * IP-based suspicious activity detection
 */
const suspiciousIPs = new Map<string, { score: number; lastActivity: Date }>();

export const detectSuspiciousActivity = (req: Request, res: Response, next: NextFunction) => {
  const ip = req.ip || 'unknown';
  const now = new Date();

  const activity = suspiciousIPs.get(ip) || { score: 0, lastActivity: now };

  // Reset score if last activity was more than 1 hour ago
  if (now.getTime() - activity.lastActivity.getTime() > 60 * 60 * 1000) {
    activity.score = 0;
  }

  // Increase score for certain suspicious patterns
  const userAgent = req.get('User-Agent') || '';
  const referer = req.get('Referer') || '';

  // Check for bot-like behavior
  if (!userAgent || userAgent.length < 10 || /bot|crawler|spider/i.test(userAgent)) {
    activity.score += 2;
  }

  // Check for rapid requests (if called multiple times quickly)
  if (now.getTime() - activity.lastActivity.getTime() < 1000) {
    activity.score += 1;
  }

  activity.lastActivity = now;
  suspiciousIPs.set(ip, activity);

  // Block if score is too high
  if (activity.score > 10) {
    logger.warn('Suspicious activity detected, blocking IP', {
      ip,
      score: activity.score,
      userAgent,
      referer,
    } as LoggingContext);

    return next(new HttpException(429, 'Suspicious activity detected. Access temporarily blocked.'));
  }

  next();
};

/**
 * Password strength validator middleware
 */
export const validatePasswordStrength = (req: Request, res: Response, next: NextFunction) => {
  const { password, newPassword } = req.body || {};
  const passwordToCheck = newPassword || password;

  if (!passwordToCheck) {
    return next();
  }

  // Import password validation from utils
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { PasswordUtils } = require('../utils/password');
  const validation = PasswordUtils.validatePasswordStrength(passwordToCheck);

  if (!validation.isValid) {
    return next(
      new HttpException(400, `Password does not meet security requirements: ${validation.feedback.join(', ')}`),
    );
  }

  next();
};

/**
 * Device fingerprinting (basic implementation)
 */
export const deviceFingerprint = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent') || '';
  const acceptLanguage = req.get('Accept-Language') || '';
  const acceptEncoding = req.get('Accept-Encoding') || '';

  // Create a simple device fingerprint
  const fingerprint = Buffer.from(`${userAgent}:${acceptLanguage}:${acceptEncoding}`).toString('base64');

  // Store fingerprint in request for potential use
  (req as unknown as Record<string, unknown>).deviceFingerprint = fingerprint;

  next();
};

/**
 * General API rate limiting middleware
 */
export const generalApiRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = req.ip || 'unknown';
    await generalApiLimiter.consume(key);
    next();
  } catch (rateLimiterRes: any) {
    const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

    logger.warn('General API rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      retryAfter: secs,
    });

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Too many requests. Try again in ${secs} seconds.`));
  }
};

/**
 * Strict rate limiting for sensitive operations
 */
export const strictRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = `${req.ip || 'unknown'}_${req.user?.id || 'anonymous'}`;
    await strictLimiter.consume(key);
    next();
  } catch (rateLimiterRes: any) {
    const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

    logger.warn('Strict rate limit exceeded', {
      ip: req.ip,
      userId: req.user?.id,
      path: req.path,
      method: req.method,
      retryAfter: secs,
    });

    res.set('Retry-After', String(secs));
    next(new HttpException(429, `Rate limit exceeded for sensitive operation. Try again in ${secs} seconds.`));
  }
};

/**
 * Get rate limiting statistics
 */
export const getRateLimitStats = async (): Promise<any> => {
  try {
    // This is a placeholder function for testing
    // In a real implementation, you would query Redis for actual stats
    return {
      totalRequests: 0,
      blockedRequests: 0,
      activeKeys: 0,
    };
  } catch (error) {
    logger.error('Failed to get rate limit stats', { error });
    throw new HttpException(500, 'Failed to get rate limit stats');
  }
};

// Export all middlewares
export default {
  loginRateLimit,
  registerRateLimit,
  passwordChangeRateLimit,
  emailVerificationRateLimit,
  generalApiRateLimit,
  strictRateLimit,
  securityHeaders,
  sanitizeInput,
  trackFailedLogin,
  trackSuccessfulLogin,
  checkAccountLockout,
  detectSuspiciousActivity,
  validatePasswordStrength,
  deviceFingerprint,
  getRateLimitStats,
};
