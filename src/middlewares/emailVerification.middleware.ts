import { Request, Response, NextFunction } from 'express';
import { HttpException } from '../exceptions/HttpException';

/**
 * Middleware to ensure the authenticated user has verified their email address.
 * This should be used on sensitive routes like password changes, profile updates,
 * and admin endpoints.
 *
 * Prerequisites:
 * - User must be authenticated (authMiddleware should run before this)
 * - User object must be attached to req.user
 *
 * @param req - Express request object with authenticated user
 * @param res - Express response object
 * @param next - Express next function
 */
const ensureEmailVerified = (req: Request, res: Response, next: NextFunction) => {
  const { user } = req;

  // Ensure user is authenticated (this should be handled by authMiddleware)
  if (!user) {
    return next(new HttpException(401, 'Authentication required'));
  }

  // Check if email is verified
  if (!user.auth?.isEmailVerified) {
    return next(
      new HttpException(403, 'Email verification required. Please verify your email address to access this resource.'),
    );
  }

  // User has verified email, proceed to next middleware/handler
  next();
};

export default ensureEmailVerified;
