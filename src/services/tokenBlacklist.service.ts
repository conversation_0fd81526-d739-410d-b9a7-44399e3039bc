import { logger } from '../utils/secureLogger';
import { redis } from '../config/redis';
import { JwtUtils } from '../utils/jwt';

/**
 * Redis-based Token Blacklist Service
 * Manages blacklisted JWT tokens using Redis for distributed blacklisting across multiple instances
 * Provides automatic expiration and memory-efficient storage
 */
export class TokenBlacklistService {
  private static readonly BLACKLIST_PREFIX = 'blacklist:';
  private static readonly CLEANUP_BATCH_SIZE = 100;

  /**
   * Get Redis key for token blacklist entry
   */
  private static getBlacklistKey(tokenId: string): string {
    return `${this.BLACKLIST_PREFIX}${tokenId}`;
  }

  /**
   * Add token to blacklist with automatic expiration
   * @param tokenId - JWT ID (jti) of the token to blacklist
   * @param reason - Reason for blacklisting (optional)
   */
  static async blacklistToken(tokenId: string, reason?: string): Promise<void> {
    try {
      if (!tokenId) {
        logger.warn('Attempted to blacklist token with empty ID');
        return;
      }

      // Get token expiration time to set Redis TTL
      const expirationTime = JwtUtils.getTokenExpiry(tokenId);
      const ttlSeconds = Math.max(0, Math.floor((expirationTime - Date.now()) / 1000));

      if (ttlSeconds <= 0) {
        logger.warn('Attempting to blacklist expired token', {
          tokenId: tokenId.substring(0, 8) + '...',
          reason: reason || 'logout'
        });
        return;
      }

      const key = this.getBlacklistKey(tokenId);
      const value = JSON.stringify({
        reason: reason || 'logout',
        blacklistedAt: new Date().toISOString(),
        expiresAt: new Date(expirationTime).toISOString()
      });

      // Set with TTL to automatically expire
      await redis.setex(key, ttlSeconds, value);

      logger.info('Token blacklisted successfully', {
        tokenId: tokenId.substring(0, 8) + '...',
        reason: reason || 'logout',
        ttlSeconds
      });
    } catch (error) {
      logger.error('Failed to blacklist token', {
        tokenId: tokenId.substring(0, 8) + '...',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Check if token is blacklisted
   * @param tokenId - JWT ID (jti) to check
   * @returns True if token is blacklisted
   */
  static async isTokenBlacklisted(tokenId: string): Promise<boolean> {
    try {
      if (!tokenId) {
        return false;
      }

      const key = this.getBlacklistKey(tokenId);
      const result = await redis.get(key);
      const isBlacklisted = result !== null;

      if (isBlacklisted) {
        logger.warn('Blacklisted token access attempt detected', {
          tokenId: tokenId.substring(0, 8) + '...',
          blacklistInfo: result ? JSON.parse(result) : null
        });
      }

      return isBlacklisted;
    } catch (error) {
      logger.error('Failed to check token blacklist status', {
        tokenId: tokenId.substring(0, 8) + '...',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      // Fail secure - assume blacklisted if we can't check
      return true;
    }
  }

  /**
   * Remove token from blacklist (for testing or administrative purposes)
   * @param tokenId - JWT ID to remove from blacklist
   */
  static async removeFromBlacklist(tokenId: string): Promise<void> {
    try {
      const key = this.getBlacklistKey(tokenId);
      const result = await redis.del(key);

      if (result > 0) {
        logger.info('Token removed from blacklist', {
          tokenId: tokenId.substring(0, 8) + '...',
        });
      }
    } catch (error) {
      logger.error('Failed to remove token from blacklist', {
        tokenId: tokenId.substring(0, 8) + '...',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Get blacklist statistics (for monitoring)
   */
  static async getBlacklistStats(): Promise<{ totalBlacklistedTokens: number; redisConnected: boolean }> {
    try {
      const keys = await redis.keys(`${this.BLACKLIST_PREFIX}*`);
      const redisConnected = await redis.ping() === 'PONG';

      return {
        totalBlacklistedTokens: keys.length,
        redisConnected
      };
    } catch (error) {
      logger.error('Failed to get blacklist statistics', { error });
      return {
        totalBlacklistedTokens: 0,
        redisConnected: false
      };
    }
  }

  /**
   * Clear all blacklisted tokens (for testing purposes only)
   */
  static async clearBlacklist(): Promise<void> {
    try {
      const keys = await redis.keys(`${this.BLACKLIST_PREFIX}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
      logger.warn('Token blacklist cleared - this should only be used in testing', {
        clearedTokens: keys.length
      });
    } catch (error) {
      logger.error('Failed to clear blacklist', { error });
      throw error;
    }
  }

  /**
   * Batch blacklist multiple tokens (for bulk operations)
   */
  static async blacklistTokens(tokenIds: string[], reason?: string): Promise<void> {
    try {
      const pipeline = redis.pipeline();
      let validTokens = 0;

      for (const tokenId of tokenIds) {
        if (!tokenId) continue;

        const expirationTime = JwtUtils.getTokenExpiry(tokenId);
        const ttlSeconds = Math.max(0, Math.floor((expirationTime - Date.now()) / 1000));

        if (ttlSeconds > 0) {
          const key = this.getBlacklistKey(tokenId);
          const value = JSON.stringify({
            reason: reason || 'bulk_operation',
            blacklistedAt: new Date().toISOString(),
            expiresAt: new Date(expirationTime).toISOString()
          });

          pipeline.setex(key, ttlSeconds, value);
          validTokens++;
        }
      }

      if (validTokens > 0) {
        await pipeline.exec();
        logger.info('Bulk token blacklist operation completed', {
          totalTokens: tokenIds.length,
          validTokens,
          reason: reason || 'bulk_operation'
        });
      }
    } catch (error) {
      logger.error('Failed to blacklist tokens in bulk', {
        tokenCount: tokenIds.length,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }
}

export default TokenBlacklistService;
