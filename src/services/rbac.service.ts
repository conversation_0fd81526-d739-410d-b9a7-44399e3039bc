import { User } from '@prisma/client';
import {
  Resource,
  Action,
  Permission,
  PermissionCondition,
  RolePermissions,
  PermissionCheckResult,
  DEFAULT_ROLE_PERMISSIONS,
} from '../types/rbac';
import { BaseResourceData } from '../types/template';

export class RBACService {
  private static rolePermissions: Map<string, Permission[]> = new Map();

  /**
   * Initialize RBAC system with default or custom permissions
   */
  static initialize(customPermissions?: RolePermissions[]) {
    const permissions = customPermissions || DEFAULT_ROLE_PERMISSIONS;

    permissions.forEach((rolePermission: RolePermissions) => {
      this.rolePermissions.set(rolePermission.role, rolePermission.permissions);
    });
  }

  /**
   * Add or update permissions for a role
   */
  static setRolePermissions(role: string, permissions: Permission[]) {
    this.rolePermissions.set(role, permissions);
  }

  /**
   * Get permissions for a specific role
   */
  static getRolePermissions(role: string): Permission[] {
    return this.rolePermissions.get(role) || [];
  }

  /**
   * Check if user has permission to perform an action on a resource
   */
  static checkPermission(
    user: User,
    resource: Resource,
    action: Action,
    resourceData?: BaseResourceData,
  ): PermissionCheckResult {
    if (!user) return { allowed: false, reason: 'User not authenticated' };
    const userRole = user.role.toString();
    const permissions = this.getRolePermissions(userRole);

    // Find matching resource permission
    const resourcePermission = permissions.find(p => p.resource === resource);

    if (!resourcePermission) {
      return {
        allowed: false,
        reason: `No permissions defined for resource '${resource}' and role '${userRole}'`,
      };
    }

    // Check if action is allowed (including MANAGE which grants all actions)
    const hasAction = resourcePermission.actions.includes(action) || resourcePermission.actions.includes(Action.MANAGE);

    if (!hasAction) {
      return {
        allowed: false,
        reason: `Action '${action}' not allowed for resource '${resource}' and role '${userRole}'`,
      };
    }

    // Check conditions if they exist
    if (resourcePermission.conditions && resourceData) {
      const conditionResult = this.evaluateConditions(
        user,
        resourcePermission.conditions as PermissionCondition[],
        resourceData,
      );

      if (!conditionResult.allowed) {
        return conditionResult;
      }
    }

    return { allowed: true };
  }

  /**
   * Check multiple permissions at once
   */
  static checkMultiplePermissions(
    user: User,
    checks: Array<{ resource: Resource; action: Action; resourceData?: BaseResourceData }>,
  ): { [key: string]: PermissionCheckResult } {
    const results: { [key: string]: PermissionCheckResult } = {};

    checks.forEach((check, index) => {
      const key = `${check.resource}_${check.action}_${index}`;
      results[key] = this.checkPermission(user, check.resource, check.action, check.resourceData);
    });

    return results;
  }

  /**
   * Get all allowed actions for a user on a specific resource
   */
  static getAllowedActions(user: User, resource: Resource): Action[] {
    if (!user) return [];

    const userRole = user.role.toString();
    const permissions = this.getRolePermissions(userRole);

    const resourcePermission = permissions.find(p => p.resource === resource);

    if (!resourcePermission) {
      return [];
    }

    // If user has MANAGE permission, return all basic CRUD actions
    if (resourcePermission.actions.includes(Action.MANAGE)) {
      return [Action.CREATE, Action.READ, Action.UPDATE, Action.DELETE];
    }

    return resourcePermission.actions;
  }

  /**
   * Check if user can access any part of a resource (useful for menu/UI rendering)
   */
  static canAccessResource(user: User, resource: Resource): boolean {
    if (!user) return false;

    const userRole = user.role.toString();
    const permissions = this.getRolePermissions(userRole);

    return permissions.some(p => p.resource === resource);
  }

  /**
   * Evaluate permission conditions
   */
  private static evaluateConditions(
    user: User,
    conditions: PermissionCondition[],
    resourceData: BaseResourceData,
  ): PermissionCheckResult {
    for (const condition of conditions) {
      const result = this.evaluateSingleCondition(user, condition, resourceData);
      if (!result.allowed) {
        return result;
      }
    }

    return { allowed: true };
  }

  /**
   * Evaluate a single permission condition
   */
  private static evaluateSingleCondition(
    user: User,
    condition: PermissionCondition,
    resourceData: BaseResourceData,
  ): PermissionCheckResult {
    const { field, operator, value } = condition;

    // Special handling for 'owns' operator
    if (operator === 'owns') {
      if (value === 'self') {
        // Check if the resource belongs to the user
        const resourceUserId = resourceData.userId || resourceData.createdBy || resourceData.ownerId;
        if (resourceUserId !== user.id) {
          return {
            allowed: false,
            reason: `User does not own this resource`,
          };
        }
      }
      return { allowed: true };
    }

    // Get the field value from resource data
    const resourceValue = this.getNestedValue(resourceData, field);

    // Evaluate condition based on operator
    switch (operator) {
      case 'eq':
        return {
          allowed: resourceValue === value,
          reason: resourceValue !== value ? `Field '${field}' must equal '${value}'` : undefined,
        };

      case 'ne':
        return {
          allowed: resourceValue !== value,
          reason: resourceValue === value ? `Field '${field}' must not equal '${value}'` : undefined,
        };

      case 'in':
        return {
          allowed: Array.isArray(value) && value.includes(resourceValue as never),
          reason:
            !Array.isArray(value) || !value.includes(resourceValue as never)
              ? `Field '${field}' must be one of: ${value}`
              : undefined,
        };

      case 'nin':
        return {
          allowed: !Array.isArray(value) || !value.includes(resourceValue as never),
          reason:
            Array.isArray(value) && value.includes(resourceValue as never)
              ? `Field '${field}' must not be one of: ${value}`
              : undefined,
        };

      case 'gt':
        return {
          allowed: (resourceValue as number) > (value as number),
          reason:
            (resourceValue as number) <= (value as number)
              ? `Field '${field}' must be greater than '${value}'`
              : undefined,
        };

      case 'gte':
        return {
          allowed: (resourceValue as number) >= (value as number),
          reason:
            (resourceValue as number) < (value as number)
              ? `Field '${field}' must be greater than or equal to '${value}'`
              : undefined,
        };

      case 'lt':
        return {
          allowed: (resourceValue as number) < (value as number),
          reason:
            (resourceValue as number) >= (value as number)
              ? `Field '${field}' must be less than '${value}'`
              : undefined,
        };

      case 'lte':
        return {
          allowed: (resourceValue as number) <= (value as number),
          reason:
            (resourceValue as number) > (value as number)
              ? `Field '${field}' must be less than or equal to '${value}'`
              : undefined,
        };

      default:
        return {
          allowed: false,
          reason: `Unknown operator: ${operator}`,
        };
    }
  }

  /**
   * Get nested object value by dot notation path
   */
  private static getNestedValue(obj: BaseResourceData | undefined, path: string): unknown {
    if (!obj) return undefined;

    return path.split('.').reduce<unknown>((current, key) => {
      if (current && typeof current === 'object' && current !== null && key in current) {
        return (current as BaseResourceData)[key as keyof BaseResourceData];
      }
      return undefined;
    }, obj);
  }

  /**
   * Create a custom role with specific permissions (helper method)
   */
  static createCustomRole(roleName: string, description: string, permissions: Permission[]) {
    this.setRolePermissions(roleName, permissions);
    return {
      role: roleName,
      description,
      permissions,
    };
  }

  /**
   * Get all configured roles
   */
  static getAllRoles(): string[] {
    return Array.from(this.rolePermissions.keys());
  }

  /**
   * Remove a role
   */
  static removeRole(role: string) {
    this.rolePermissions.delete(role);
  }
}
