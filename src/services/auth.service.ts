import { User, Role } from '@prisma/client';
import { UserRepository } from '../repositories/user.repository';
import { HttpException } from '../exceptions/HttpException';
import { LoginDto, ChangePasswordDto } from '../dtos/auth.dto';
import { CreateUserDto, UpdateUserDto } from '../dtos/user.dto';
import { logger } from '../utils/secureLogger';
import { sanitizeForLog, sanitizeError, sanitizeAuthEvent } from '../utils/logSanitizer';
import { JwtUtils, JwtTokenPayload } from '../utils/jwt';
import { PasswordUtils } from '../utils/password';
import { RepositoryFilters, AuthEvent } from '../types/template';
import { TokenBlacklistService } from './tokenBlacklist.service';
import { SessionVersioningService } from './sessionVersioning.service';
import { EmailVerificationService } from './emailVerification.service';
import { v4 as uuidv4 } from 'uuid';

export interface AuthResponse {
  user: Omit<User, 'passwordHash'>;
  token: string;
  refreshToken?: string;
  expiresIn: string;
  tokenType: 'Bearer';
  emailVerificationRequired?: boolean;
}

export interface AuthContext {
  ip?: string;
  userAgent?: string;
  deviceFingerprint?: string;
}

export class AuthService {
  /**
   * Register a new user
   */
  static async signUp(userData: CreateUserDto, context?: AuthContext): Promise<AuthResponse> {
    const { email, password, username } = userData;

    try {
      if (password) {
        // Validate password strength
        const passwordValidation = PasswordUtils.validatePasswordStrength(password);
        if (!passwordValidation.isValid) {
          throw new HttpException(
            400,
            `Password does not meet security requirements: ${passwordValidation.feedback.join(', ')}`,
          );
        }
      }

      // Check if user already exists
      if (username) {
        const existingUserByUsername = await UserRepository.findByUsername(username);
        if (existingUserByUsername) {
          throw new HttpException(409, 'User with this username already exists');
        }
      }
      if (email) {
        const existingUserByEmail = await UserRepository.findByEmail(email);
        if (existingUserByEmail) {
          throw new HttpException(409, 'User with this email already exists');
        }
      }

      // Hash password
      const passwordHash = password ? await PasswordUtils.hash(password) : undefined;

      // Create user
      const user = await UserRepository.create({
        email: email!,
        username: username,
        passwordHash: passwordHash!,
      });

      // Send email verification if email is provided
      if (user.email) {
        const emailVerificationService = new EmailVerificationService();
        await emailVerificationService.createAndSend(user, {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        });
      }

      // Generate JWT tokens
      const token = await this.generateToken(user);
      const refreshToken = await this.generateRefreshToken(user);

      // Log registration event
      const authEvent: AuthEvent = {
        type: 'AUTH_REGISTER',
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      logger.info('User registered successfully', sanitizeAuthEvent(authEvent));

      return {
        user: this.sanitizeUser(user),
        token,
        refreshToken,
        expiresIn: '7d',
        tokenType: 'Bearer',
        emailVerificationRequired: true,
      };
    } catch (error) {
      logger.error(
        'User registration failed',
        sanitizeForLog({
          username,
          email,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Login user
   */
  static async logIn(loginData: LoginDto, context?: AuthContext): Promise<AuthResponse> {
    const { username, email, password } = loginData;

    try {
      // Find user by username or email
      let user: User | null = null;
      if (username) {
        user = await UserRepository.findByUsername(username);
      } else if (email) {
        user = await UserRepository.findByEmail(email);
      }

      // Check for account lockout
      if (user && user.lockoutExpires && new Date() < user.lockoutExpires) {
        throw new HttpException(423, 'Account temporarily locked due to too many failed login attempts');
      }

      if (!user || !user.passwordHash) {
        throw new HttpException(401, 'Invalid credentials');
      }

      // Verify password
      const isPasswordValid = await PasswordUtils.compare(password, user.passwordHash);

      if (!isPasswordValid) {
        const failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
        let lockoutExpires: Date | null = null;
        if (failedLoginAttempts >= 5) {
          lockoutExpires = new Date(Date.now() + 15 * 60 * 1000); // Lock for 15 minutes
        }
        await UserRepository.update(user.id, { failedLoginAttempts, lockoutExpires });
        throw new HttpException(401, 'Invalid credentials');
      }

      // Reset failed login attempts on successful login
      if (user.failedLoginAttempts > 0) {
        await UserRepository.update(user.id, { failedLoginAttempts: 0, lockoutExpires: null });
      }

      // Generate JWT tokens
      const token = await this.generateToken(user);
      const refreshToken = await this.generateRefreshToken(user);

      // Log login event
      const authEvent: AuthEvent = {
        type: 'AUTH_LOGIN',
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      logger.info('User logged in successfully', sanitizeAuthEvent(authEvent));

      return {
        user: this.sanitizeUser(user),
        token,
        refreshToken,
        expiresIn: '7d',
        tokenType: 'Bearer',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      logger.error(
        'User login failed',
        sanitizeForLog({
          username,
          email,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Get user profile
   */
  static async getProfile(userId: string): Promise<Omit<User, 'passwordHash'>> {
    try {
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      return this.sanitizeUser(user);
    } catch (error) {
      logger.error(
        'Failed to get user profile',
        sanitizeForLog({
          userId,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId: string, updateData: UpdateUserDto): Promise<Omit<User, 'passwordHash'>> {
    try {
      const user = await UserRepository.update(userId, updateData);

      logger.info('User profile updated successfully', sanitizeForLog({ userId }));

      return this.sanitizeUser(user);
    } catch (error) {
      logger.error(
        'Failed to update user profile',
        sanitizeForLog({
          userId,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Change user password
   */
  static async changePassword(userId: string, passwordData: ChangePasswordDto, context?: AuthContext): Promise<void> {
    const { currentPassword, newPassword } = passwordData;

    try {
      // Validate new password strength
      const passwordValidation = PasswordUtils.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        throw new HttpException(
          400,
          `New password does not meet security requirements: ${passwordValidation.feedback.join(', ')}`,
        );
      }

      // Find user
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      // Verify current password
      if (!user.passwordHash) {
        throw new HttpException(400, 'User does not have a password set.');
      }
      const isCurrentPasswordValid = await PasswordUtils.compare(currentPassword, user.passwordHash);
      if (!isCurrentPasswordValid) {
        throw new HttpException(400, 'Current password is incorrect');
      }

      // Check if new password is same as current (prevent password reuse)
      const isSamePassword = await PasswordUtils.compare(newPassword, user.passwordHash);
      if (isSamePassword) {
        throw new HttpException(400, 'New password must be different from current password');
      }

      // Hash new password
      const newPasswordHash = await PasswordUtils.hash(newPassword);

      // Update password
      await UserRepository.update(userId, { passwordHash: newPasswordHash });

      // Log password change event
      const authEvent: AuthEvent = {
        type: 'AUTH_PASSWORD_CHANGE',
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      logger.info('Password changed successfully', sanitizeAuthEvent(authEvent));
    } catch (error) {
      logger.error(
        'Failed to change password',
        sanitizeForLog({
          userId,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Get all users (admin only)
   */
  static async getAllUsers(page: number = 1, limit: number = 10, filters: RepositoryFilters = {}) {
    try {
      const result = await UserRepository.findMany(filters, page, limit);

      return {
        ...result,
        data: result.data.map(user => this.sanitizeUser(user)),
      };
    } catch (error) {
      logger.error(
        'Failed to get all users',
        sanitizeForLog({
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Update user role (admin only)
   * SECURITY: Invalidates all existing user tokens to prevent privilege persistence
   */
  static async updateUserRole(userId: string, role: Role): Promise<Omit<User, 'passwordHash'>> {
    try {
      // First, get the current user to log role change
      const currentUser = await UserRepository.findById(userId);
      if (!currentUser) {
        throw new HttpException(404, 'User not found');
      }

      // Update the user role
      const user = await UserRepository.update(userId, { role });

      // SECURITY FIX: Invalidate all user tokens to prevent privilege persistence
      // Note: This is a simplified approach. In production, you might want to:
      // 1. Track user tokens in database/Redis with user association
      // 2. Implement user-specific token invalidation
      // 3. Add a "token version" field to user records

      // For now, we'll log this security event and recommend immediate logout
      const authEvent: AuthEvent = {
        type: 'AUTH_ROLE_CHANGE' as any, // Extend AuthEvent type to include this
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          previousRole: currentUser.role,
          newRole: role,
          adminAction: true,
        },
      };

      logger.warn(
        'SECURITY: User role changed - existing tokens may retain old privileges until expiry',
        sanitizeAuthEvent(authEvent),
      );

      logger.info(
        'User role updated successfully',
        sanitizeForLog({
          userId,
          previousRole: currentUser.role,
          newRole: role,
        }),
      );

      return this.sanitizeUser(user);
    } catch (error) {
      logger.error(
        'Failed to update user role',
        sanitizeForLog({
          userId,
          role,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Delete user (admin only)
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      await UserRepository.delete(userId);

      logger.info('User deleted successfully', sanitizeForLog({ userId }));
    } catch (error) {
      logger.error(
        'Failed to delete user',
        sanitizeForLog({
          userId,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Generate JWT token with unique JTI for blacklist tracking
   */
  private static async generateToken(user: User): Promise<string> {
    const payload: JwtTokenPayload & { jti: string } = {
      id: user.id,
      email: user.email!,
      role: user.role,
      jti: uuidv4(), // Unique token identifier for blacklisting
    };

    return await JwtUtils.sign(payload as JwtTokenPayload);
  }

  /**
   * Generate refresh token with unique JTI for blacklist tracking
   */
  private static async generateRefreshToken(user: User): Promise<string> {
    const payload: JwtTokenPayload & { jti: string } = {
      id: user.id,
      email: user.email!,
      role: user.role,
      jti: uuidv4(), // Unique token identifier for blacklisting
    };

    return await JwtUtils.signRefreshToken(payload as JwtTokenPayload);
  }

  /**
   * Refresh access token using refresh token with secure rotation
   */
  static async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      // Check if token is blacklisted
      const refreshTokenId = JwtUtils.getTokenId(refreshToken);
      if (refreshTokenId && TokenBlacklistService.isTokenBlacklisted(refreshTokenId)) {
        logger.warn(
          'Attempted use of blacklisted refresh token',
          sanitizeForLog({
            tokenId: refreshTokenId?.substring(0, 8) + '***',
          }),
        );
        throw new HttpException(401, 'Invalid refresh token');
      }

      // Verify refresh token
      const decoded = await JwtUtils.verify(refreshToken);

      // Find user
      const user = await UserRepository.findById(decoded.id);
      if (!user) {
        throw new HttpException(401, 'Invalid refresh token');
      }

      // Generate new token pair
      const newToken = await this.generateToken(user);
      const newRefreshToken = await this.generateRefreshToken(user);

      // Blacklist old refresh token (prevents token reuse)
      if (refreshTokenId) {
        await TokenBlacklistService.blacklistToken(refreshTokenId, 'Token rotation');
      }

      logger.info(
        'Token refreshed successfully',
        sanitizeForLog({
          userId: user.id,
          oldTokenId: refreshTokenId?.substring(0, 8) + '***',
        }),
      );

      return {
        user: this.sanitizeUser(user),
        token: newToken,
        refreshToken: newRefreshToken,
        expiresIn: JwtUtils.getTokenExpiryMs().access / (60 * 1000) + 'm', // Convert to minutes string
        tokenType: 'Bearer',
      };
    } catch (error) {
      logger.error(
        'Token refresh failed',
        sanitizeForLog({
          error: sanitizeError(error),
        }),
      );
      throw new HttpException(401, 'Invalid refresh token');
    }
  }

  /**
   * Logout user and blacklist tokens
   */
  static async logout(accessToken?: string, refreshToken?: string): Promise<void> {
    try {
      // Blacklist access token if provided
      if (accessToken) {
        const accessTokenId = JwtUtils.getTokenId(accessToken);
        if (accessTokenId) {
          await TokenBlacklistService.blacklistToken(accessTokenId, 'User logout');
        }
      }

      // Blacklist refresh token if provided
      if (refreshToken) {
        const refreshTokenId = JwtUtils.getTokenId(refreshToken);
        if (refreshTokenId) {
          await TokenBlacklistService.blacklistToken(refreshTokenId, 'User logout');
        }
      }
    } catch (error) {
      logger.error(
        'User logout failed',
        sanitizeForLog({
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Find user by email
   */
  static async findUserByEmail(email: string): Promise<Omit<User, 'passwordHash'> | null> {
    try {
      const user = await UserRepository.findByEmail(email);
      if (!user) {
        return null;
      }
      return this.sanitizeUser(user);
    } catch (error) {
      logger.error(
        'Failed to find user by email',
        sanitizeForLog({
          email,
          error: sanitizeError(error),
        }),
      );
      throw error;
    }
  }

  /**
   * Sanitize user object to remove sensitive data
   */
  private static sanitizeUser(user: User): Omit<User, 'passwordHash'> {
    const { ...sanitizedUser } = user;
    return sanitizedUser;
  }

  /**
   * Handle user role change with session versioning
   */
  static async handleRoleChange(userId: string, newRole: string): Promise<void> {
    try {
      const user = await UserRepository.findById(userId);
      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      const oldRole = user.role;

      // Update user role
      await UserRepository.update(userId, { role: newRole });

      // Handle session versioning
      await SessionVersioningService.handleRoleChange(userId, oldRole, newRole);

      logger.info('User role changed with session versioning', {
        userId,
        oldRole,
        newRole,
      });
    } catch (error) {
      logger.error('Failed to handle role change', {
        userId,
        newRole,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Handle password change with session versioning
   */
  static async handlePasswordChange(userId: string, newPassword: string): Promise<void> {
    try {
      // Hash new password
      const passwordHash = await PasswordUtils.hash(newPassword);

      // Update user password
      await UserRepository.update(userId, { passwordHash });

      // Handle session versioning
      await SessionVersioningService.handlePasswordChange(userId);

      logger.info('User password changed with session versioning', { userId });
    } catch (error) {
      logger.error('Failed to handle password change', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Initialize session versioning after successful authentication
   */
  static async initializeUserSession(req: any, user: User): Promise<void> {
    try {
      if (req.session) {
        await SessionVersioningService.initializeSession(req, user);
      }
    } catch (error) {
      logger.error('Failed to initialize user session', {
        userId: user.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      // Don't throw error to avoid breaking authentication flow
    }
  }
}
