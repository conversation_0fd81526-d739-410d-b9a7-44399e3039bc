# Quick Start Guide

## Overview

This guide will help you get up and running with the Secure Backend Template in under 10 minutes. This template provides a production-ready Node.js backend with enterprise-grade security features, authentication, and authorization.

## Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v18 or higher)
- npm or yarn package manager
- PostgreSQL database
- Git

## Initial Setup

### 1. Environment Configuration

Create environment-specific configuration files:

**For Development:**
Create `.env.development.local` in the root directory:
```env
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://username:password@localhost:5432/your_database
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
JWT_EXPIRES_IN=7d
BCRYPT_SALT_ROUNDS=12
```

**For Production:**
Create `.env.production.local`:
```env
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://username:password@localhost:5432/your_production_database
JWT_SECRET=your-production-jwt-secret-key
JWT_EXPIRES_IN=7d
ORIGIN=https://yourdomain.com
CREDENTIALS=true
```

### 2. Database Setup

**Initialize Database:**
```bash
# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate deploy

# (Optional) Seed database with sample data
npx prisma db seed
```

### 3. Start the Application

**Development Mode:**
```bash
npm run dev
```

**Production Mode:**
```bash
npm run build
npm start
```

The server will start on the configured port (default: 3000).

## API Endpoints Overview

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/auth/register` | Register new user | No |
| POST | `/api/v1/auth/login` | User login | No |
| POST | `/api/v1/auth/refresh` | Refresh access token | No |
| POST | `/api/v1/auth/logout` | User logout | No |
| GET | `/api/v1/auth/profile` | Get user profile | Yes |
| PATCH | `/api/v1/auth/profile` | Update profile | Yes |
| PATCH | `/api/v1/auth/change-password` | Change password | Yes |

### User Management (Admin Only)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/auth/users` | Get all users | Admin |
| PATCH | `/api/v1/auth/users/:userId/role` | Update user role | Admin |
| DELETE | `/api/v1/auth/users/:userId` | Delete user | Admin |

### Utility Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check |
| GET | `/api-docs` | API documentation |

## Authentication Flow

### 1. User Registration

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "role": "USER"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "role": "USER"
    },
    "token": "jwt-access-token",
    "expiresIn": "7d",
    "tokenType": "Bearer"
  }
}
```

### 2. User Login

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response:**
- Access token in response body
- Refresh token set as HTTP-only cookie
- User profile information

### 3. Making Authenticated Requests

Include the access token in the Authorization header:
```
Authorization: Bearer <your-access-token>
```

### 4. Token Refresh

When the access token expires, use the refresh endpoint:
```json
POST /api/v1/auth/refresh
```

The refresh token is automatically sent via cookie.

## Security Features

### Built-in Security

- **JWT Authentication**: Stateless token-based authentication
- **Password Security**: bcrypt hashing with configurable salt rounds
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Comprehensive request validation with sanitization
- **CORS Protection**: Configurable cross-origin resource sharing
- **Security Headers**: Helmet.js for security headers
- **SQL Injection Protection**: Prisma ORM with parameterized queries

### Access Control

- **Role-Based Access Control (RBAC)**: USER and ADMIN roles
- **Permission-Based Authorization**: Fine-grained resource permissions
- **Middleware Protection**: Multiple layers of security middleware
- **Resource Access Control**: Granular access to specific resources

## Configuration Options

### JWT Configuration

- `JWT_SECRET`: Secret key for token signing (minimum 32 characters)
- `JWT_EXPIRES_IN`: Token expiration time (e.g., "7d", "24h")

### Security Configuration

- `BCRYPT_SALT_ROUNDS`: Password hashing complexity (default: 12)
- `RATE_LIMIT_WINDOW_MS`: Rate limiting window (default: 15 minutes)
- `RATE_LIMIT_MAX`: Maximum requests per window (default: 100)

### Database Configuration

- `DATABASE_URL`: PostgreSQL connection string
- Connection pooling and timeout settings are handled automatically

## Development Features

### API Documentation

Access interactive API documentation at:
- Development: `http://localhost:3000/api-docs`
- Production: `https://yourdomain.com/api-docs`

### Logging

- **Development**: Console logging with colors
- **Production**: File-based logging with daily rotation
- **Security Events**: Comprehensive audit trail

### Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test suite
npm run test:unit
```

## Production Deployment

### Environment Setup

1. Set `NODE_ENV=production`
2. Configure production database URL
3. Set secure JWT secrets
4. Configure CORS origins
5. Set up HTTPS certificates

### Recommended Infrastructure

- **Reverse Proxy**: nginx or Apache for SSL termination
- **Process Manager**: PM2 for process management
- **Database**: PostgreSQL with connection pooling
- **Monitoring**: Application monitoring and log aggregation
- **Backup**: Regular database backups

### Health Monitoring

The `/health` endpoint provides:
- Server status
- Uptime information
- Feature overview
- Database connectivity (when implemented)

## Troubleshooting

### Common Issues

**Database Connection Errors:**
- Verify `DATABASE_URL` format
- Ensure PostgreSQL is running
- Check database credentials

**JWT Token Errors:**
- Verify `JWT_SECRET` is set
- Ensure token hasn't expired
- Check token format in Authorization header

**Rate Limiting:**
- Check if you've exceeded request limits
- Wait for rate limit window to reset
- Configure appropriate limits for your use case

**Permission Denied:**
- Verify user role and permissions
- Check if endpoint requires admin access
- Ensure token is valid and user exists

### Support

For additional support:
1. Check the security analysis documentation
2. Review API documentation at `/api-docs`
3. Examine log files for detailed error information
4. Verify environment configuration

This quick start guide provides everything needed to get your secure backend up and running. The template is designed to be production-ready out of the box while remaining flexible for customization.
